import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { logger } from '../services/logger';
import { rateLimiter } from '../services/rate-limiter';
import { apiClient } from '../services/api';
import { STORAGE_KEYS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';

interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: 'student' | 'educator' | 'admin';
  stats: {
    totalQuizzes: number;
    averageScore: number;
    streak: number;
    rank: number;
  };
  preferences?: {
    language: string;
    notifications: boolean;
    theme: 'light' | 'dark';
  };
  createdAt?: Date;
  lastActive?: Date;
  emailVerified?: boolean;
  twoFactorEnabled?: boolean;
  permissions?: string[];
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
  refreshToken: string | null;
  sessionExpiry: number | null;
  loginAttempts: number;
  lockoutUntil: number | null;
}

interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
  twoFactorCode?: string;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  refreshAuth: () => Promise<void>;
  updateUser: (updates: Partial<User>) => void;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<void>;
  enableTwoFactor: () => Promise<string>; // Returns QR code URL
  disableTwoFactor: (code: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  checkSession: () => boolean;
  isSessionExpired: () => boolean;
  incrementLoginAttempts: () => void;
  resetLoginAttempts: () => void;
  setLockout: (duration: number) => void;
  isLockedOut: () => boolean;
}

type AuthStore = AuthState & AuthActions;

// Validation helpers
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePassword = (password: string): string | null => {
  if (password.length < 8) return 'Password must be at least 8 characters long';
  if (!/(?=.*[a-z])/.test(password)) return 'Password must contain at least one lowercase letter';
  if (!/(?=.*[A-Z])/.test(password)) return 'Password must contain at least one uppercase letter';
  if (!/(?=.*\d)/.test(password)) return 'Password must contain at least one number';
  if (!/(?=.*[@$!%*?&])/.test(password)) return 'Password must contain at least one special character';
  return null;
};

const getClientIP = (): string => {
  // In a real application, this would be handled server-side
  return 'client-ip';
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      token: null,
      refreshToken: null,
      sessionExpiry: null,
      loginAttempts: 0,
      lockoutUntil: null,

      // Actions
      login: async (credentials: LoginCredentials) => {
        const state = get();

        // Check if locked out
        if (state.isLockedOut()) {
          const timeRemaining = Math.ceil((state.lockoutUntil! - Date.now()) / 1000 / 60);
          throw new Error(`Account locked. Try again in ${timeRemaining} minutes.`);
        }

        // Check rate limiting
        const rateLimitResult = rateLimiter.checkLoginLimit(getClientIP());
        if (!rateLimitResult.allowed) {
          throw new Error(rateLimitResult.message || ERROR_MESSAGES.UNAUTHORIZED);
        }

        // Validate input
        if (!validateEmail(credentials.email)) {
          throw new Error('Please enter a valid email address');
        }

        if (!credentials.password) {
          throw new Error('Password is required');
        }

        set({ isLoading: true, error: null });

        try {
          logger.info('Login attempt', 'auth', { email: credentials.email });

          const response = await apiClient.login(credentials.email, credentials.password);

          if (!response.success) {
            state.incrementLoginAttempts();
            throw new Error(response.error || ERROR_MESSAGES.INVALID_CREDENTIALS);
          }

          const { user, token, refreshToken } = response.data;
          const sessionExpiry = Date.now() + (credentials.rememberMe ? 30 * 24 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000);

          // Store tokens securely
          localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
          if (refreshToken) {
            localStorage.setItem('refresh_token', refreshToken);
          }

          set({
            user: {
              ...user,
              lastActive: new Date(),
              preferences: {
                language: 'en',
                notifications: true,
                theme: 'light',
                ...user.preferences
              }
            },
            isAuthenticated: true,
            isLoading: false,
            error: null,
            token,
            refreshToken,
            sessionExpiry,
            loginAttempts: 0,
            lockoutUntil: null
          });

          logger.setUserId(user.id);
          logger.info('Login successful', 'auth', { userId: user.id });
          logger.trackUserAction('login', { email: credentials.email });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : ERROR_MESSAGES.UNAUTHORIZED;
          set({ error: errorMessage, isLoading: false });
          state.incrementLoginAttempts();

          logger.error('Login failed', 'auth', {
            email: credentials.email,
            error: errorMessage,
            attempts: state.loginAttempts + 1
          });

          throw error;
        }
      },

      register: async (data: RegisterData) => {
        // Validate input
        if (!data.name.trim()) {
          throw new Error('Name is required');
        }

        if (!validateEmail(data.email)) {
          throw new Error('Please enter a valid email address');
        }

        const passwordError = validatePassword(data.password);
        if (passwordError) {
          throw new Error(passwordError);
        }

        if (data.password !== data.confirmPassword) {
          throw new Error('Passwords do not match');
        }

        if (!data.acceptTerms) {
          throw new Error('You must accept the terms and conditions');
        }

        // Check rate limiting
        const rateLimitResult = rateLimiter.checkRegistrationLimit(getClientIP());
        if (!rateLimitResult.allowed) {
          throw new Error(rateLimitResult.message || 'Too many registration attempts');
        }

        set({ isLoading: true, error: null });

        try {
          logger.info('Registration attempt', 'auth', { email: data.email });

          const response = await apiClient.register({
            name: data.name,
            email: data.email,
            password: data.password
          });

          if (!response.success) {
            throw new Error(response.error || 'Registration failed');
          }

          logger.info('Registration successful', 'auth', { email: data.email });
          logger.trackUserAction('register', { email: data.email });

          set({ isLoading: false, error: null });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({ error: errorMessage, isLoading: false });

          logger.error('Registration failed', 'auth', {
            email: data.email,
            error: errorMessage
          });

          throw error;
        }
      },

      logout: () => {
        const state = get();

        logger.info('Logout', 'auth', { userId: state.user?.id });
        logger.trackUserAction('logout');

        // Clear tokens
        localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
        localStorage.removeItem('refresh_token');

        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
          token: null,
          refreshToken: null,
          sessionExpiry: null,
          loginAttempts: 0,
          lockoutUntil: null
        });
      },

      refreshAuth: async () => {
        const state = get();
        const refreshToken = state.refreshToken || localStorage.getItem('refresh_token');

        if (!refreshToken) {
          state.logout();
          return;
        }

        try {
          const response = await apiClient.request('/auth/refresh', {
            method: 'POST',
            body: { refreshToken }
          });

          if (!response.success) {
            state.logout();
            return;
          }

          const { token, user } = response.data;
          const sessionExpiry = Date.now() + 24 * 60 * 60 * 1000; // 24 hours

          localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);

          set({
            token,
            sessionExpiry,
            user: { ...state.user, ...user, lastActive: new Date() }
          });

        } catch (error) {
          logger.error('Token refresh failed', 'auth', error);
          state.logout();
        }
      },

      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          const updatedUser = {
            ...currentUser,
            ...updates,
            lastActive: new Date()
          };

          set({ user: updatedUser });

          logger.info('User updated', 'auth', {
            userId: currentUser.id,
            updates: Object.keys(updates)
          });
          logger.trackUserAction('update_profile', updates);
        }
      },

      changePassword: async (currentPassword: string, newPassword: string) => {
        const state = get();
        if (!state.user) {
          throw new Error('User not authenticated');
        }

        const passwordError = validatePassword(newPassword);
        if (passwordError) {
          throw new Error(passwordError);
        }

        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.request('/auth/change-password', {
            method: 'POST',
            body: { currentPassword, newPassword }
          });

          if (!response.success) {
            throw new Error(response.error || 'Failed to change password');
          }

          set({ isLoading: false });
          logger.info('Password changed', 'auth', { userId: state.user.id });
          logger.trackUserAction('change_password');

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to change password';
          set({ error: errorMessage, isLoading: false });
          logger.error('Password change failed', 'auth', { userId: state.user.id, error: errorMessage });
          throw error;
        }
      },

      resetPassword: async (email: string) => {
        if (!validateEmail(email)) {
          throw new Error('Please enter a valid email address');
        }

        // Check rate limiting
        const rateLimitResult = rateLimiter.checkPasswordResetLimit(email);
        if (!rateLimitResult.allowed) {
          throw new Error(rateLimitResult.message || 'Too many password reset attempts');
        }

        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.request('/auth/reset-password', {
            method: 'POST',
            body: { email }
          });

          if (!response.success) {
            throw new Error(response.error || 'Failed to send reset email');
          }

          set({ isLoading: false });
          logger.info('Password reset requested', 'auth', { email });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to send reset email';
          set({ error: errorMessage, isLoading: false });
          logger.error('Password reset failed', 'auth', { email, error: errorMessage });
          throw error;
        }
      },

      verifyEmail: async (token: string) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.request('/auth/verify-email', {
            method: 'POST',
            body: { token }
          });

          if (!response.success) {
            throw new Error(response.error || 'Email verification failed');
          }

          const state = get();
          if (state.user) {
            set({
              user: { ...state.user, emailVerified: true },
              isLoading: false
            });
          }

          logger.info('Email verified', 'auth', { userId: state.user?.id });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Email verification failed';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      enableTwoFactor: async () => {
        const state = get();
        if (!state.user) {
          throw new Error('User not authenticated');
        }

        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.request('/auth/enable-2fa', {
            method: 'POST'
          });

          if (!response.success) {
            throw new Error(response.error || 'Failed to enable two-factor authentication');
          }

          set({ isLoading: false });
          logger.info('Two-factor authentication enabled', 'auth', { userId: state.user.id });

          return response.data.qrCodeUrl;

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to enable two-factor authentication';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      disableTwoFactor: async (code: string) => {
        const state = get();
        if (!state.user) {
          throw new Error('User not authenticated');
        }

        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.request('/auth/disable-2fa', {
            method: 'POST',
            body: { code }
          });

          if (!response.success) {
            throw new Error(response.error || 'Failed to disable two-factor authentication');
          }

          set({
            user: { ...state.user, twoFactorEnabled: false },
            isLoading: false
          });

          logger.info('Two-factor authentication disabled', 'auth', { userId: state.user.id });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to disable two-factor authentication';
          set({ error: errorMessage, isLoading: false });
          throw error;
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error, isLoading: false });
      },

      clearError: () => {
        set({ error: null });
      },

      checkSession: () => {
        const state = get();
        if (!state.isAuthenticated || !state.sessionExpiry) {
          return false;
        }

        return Date.now() < state.sessionExpiry;
      },

      isSessionExpired: () => {
        const state = get();
        if (!state.sessionExpiry) {
          return true;
        }

        return Date.now() >= state.sessionExpiry;
      },

      incrementLoginAttempts: () => {
        const state = get();
        const newAttempts = state.loginAttempts + 1;

        set({ loginAttempts: newAttempts });

        // Lock account after 5 failed attempts
        if (newAttempts >= 5) {
          state.setLockout(15 * 60 * 1000); // 15 minutes
        }
      },

      resetLoginAttempts: () => {
        set({ loginAttempts: 0, lockoutUntil: null });
      },

      setLockout: (duration: number) => {
        set({ lockoutUntil: Date.now() + duration });
        logger.warn('Account locked due to failed login attempts', 'auth');
      },

      isLockedOut: () => {
        const state = get();
        if (!state.lockoutUntil) {
          return false;
        }

        if (Date.now() >= state.lockoutUntil) {
          // Lockout expired, clear it
          set({ lockoutUntil: null, loginAttempts: 0 });
          return false;
        }

        return true;
      }
    }),
    {
      name: STORAGE_KEYS.USER,
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        token: state.token,
        refreshToken: state.refreshToken,
        sessionExpiry: state.sessionExpiry
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Check if session is expired on rehydration
          if (state.isSessionExpired()) {
            state.logout();
          } else if (state.user) {
            // Set user ID for logger
            logger.setUserId(state.user.id);
          }
        }
      }
    }
  )
);