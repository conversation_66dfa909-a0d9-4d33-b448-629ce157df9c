/**
 * Enhanced Analytics Dashboard Component
 * Provides comprehensive analytics visualization and insights
 */

import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Target,
  Clock,
  Award,
  Brain,
  Users,
  BarChart3,
  Download,
  Filter,
  Calendar,
  RefreshCw
} from 'lucide-react';
import { analyticsService, PerformanceReport } from '../../services/analytics-service';
import { useAuthStore } from '../../store/authStore';
import { logger } from '../../services/logger';
import { 
  AnalyticsLineChart, 
  AnalyticsBarChart, 
  AnalyticsPieChart, 
  ProgressChart 
} from './Charts';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Loading } from '../ui/Loading';

interface AnalyticsDashboardProps {
  userId?: string;
  timeframe?: '7d' | '30d' | '90d' | '1y';
  showExportOptions?: boolean;
}

interface StatCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ComponentType<any>;
  color: string;
  trend?: 'up' | 'down' | 'neutral';
}

const StatCard: React.FC<StatCardProps> = ({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  color, 
  trend 
}) => {
  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="w-4 h-4 text-green-500" />;
    if (trend === 'down') return <TrendingDown className="w-4 h-4 text-red-500" />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return 'text-green-600';
    if (trend === 'down') return 'text-red-600';
    return 'text-gray-600';
  };

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change !== undefined && (
            <div className={`flex items-center mt-1 text-sm ${getTrendColor()}`}>
              {getTrendIcon()}
              <span className="ml-1">
                {Math.abs(change)}% from last period
              </span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </Card>
  );
};

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  userId,
  timeframe = '30d',
  showExportOptions = true
}) => {
  const { user } = useAuthStore();
  const [report, setReport] = useState<PerformanceReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);
  const [refreshing, setRefreshing] = useState(false);

  const targetUserId = userId || user?.id;

  useEffect(() => {
    if (targetUserId) {
      loadAnalytics();
    }
  }, [targetUserId, selectedTimeframe]);

  const loadAnalytics = async () => {
    if (!targetUserId) return;

    try {
      setLoading(true);
      setError(null);

      const performanceReport = await analyticsService.generatePerformanceReport(
        targetUserId,
        selectedTimeframe
      );

      setReport(performanceReport);
      logger.trackUserAction('view_analytics_dashboard', { timeframe: selectedTimeframe });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load analytics';
      setError(errorMessage);
      logger.error('Analytics dashboard load failed', 'analytics-dashboard', { error: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    analyticsService.clearCache();
    await loadAnalytics();
    setRefreshing(false);
  };

  const handleExport = async (format: 'csv' | 'json' | 'pdf') => {
    if (!targetUserId) return;

    try {
      const blob = await analyticsService.exportData(targetUserId, format);
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analytics-${selectedTimeframe}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      logger.trackUserAction('export_analytics', { format, timeframe: selectedTimeframe });
    } catch (err) {
      logger.error('Analytics export failed', 'analytics-dashboard', { format, error: err });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">
          <BarChart3 className="w-12 h-12 mx-auto" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Analytics</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={loadAnalytics} variant="primary">
          Try Again
        </Button>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-600">Start taking quizzes to see your analytics.</p>
      </div>
    );
  }

  const { overview, trends, categoryBreakdown, achievements } = report;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
          <p className="text-gray-600">Track your learning progress and performance</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Timeframe Selector */}
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 3 months</option>
            <option value="1y">Last year</option>
          </select>

          {/* Refresh Button */}
          <Button
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            icon={RefreshCw}
            loading={refreshing}
          >
            Refresh
          </Button>

          {/* Export Options */}
          {showExportOptions && (
            <div className="relative">
              <Button
                variant="outline"
                size="sm"
                icon={Download}
                onClick={() => handleExport('pdf')}
              >
                Export
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Quizzes"
          value={overview.totalQuizzes}
          change={12}
          icon={Brain}
          color="bg-blue-500"
          trend="up"
        />
        <StatCard
          title="Average Score"
          value={`${overview.averageScore}%`}
          change={overview.improvement}
          icon={Target}
          color="bg-green-500"
          trend={overview.improvement > 0 ? 'up' : overview.improvement < 0 ? 'down' : 'neutral'}
        />
        <StatCard
          title="Study Time"
          value={`${Math.round(overview.totalTime / 60)}h`}
          change={8}
          icon={Clock}
          color="bg-purple-500"
          trend="up"
        />
        <StatCard
          title="Current Streak"
          value={`${overview.streak} days`}
          icon={Award}
          color="bg-orange-500"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Score Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Score Trend</h3>
          <AnalyticsLineChart
            data={trends.scoreHistory}
            xKey="date"
            yKey="score"
            height={250}
          />
        </Card>

        {/* Time Trend */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Time per Quiz</h3>
          <AnalyticsLineChart
            data={trends.timeHistory}
            xKey="date"
            yKey="time"
            lineColor="#8B5CF6"
            height={250}
          />
        </Card>
      </div>

      {/* Category Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance by Category</h3>
            <div className="space-y-4">
              {categoryBreakdown.map((category, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{category.category}</h4>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-600">{category.quizzes} quizzes</span>
                        <span className={`text-sm font-medium ${
                          category.improvement > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {category.improvement > 0 ? '+' : ''}{category.improvement}%
                        </span>
                        <span className="text-lg font-semibold text-gray-900">{category.averageScore}%</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${category.averageScore}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* Recent Achievements */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Achievements</h3>
          <div className="space-y-3">
            {achievements.slice(0, 5).map((achievement) => (
              <div key={achievement.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className={`p-2 rounded-full ${
                  achievement.rarity === 'legendary' ? 'bg-yellow-100' :
                  achievement.rarity === 'epic' ? 'bg-purple-100' :
                  achievement.rarity === 'rare' ? 'bg-blue-100' : 'bg-gray-100'
                }`}>
                  <Award className={`w-4 h-4 ${
                    achievement.rarity === 'legendary' ? 'text-yellow-600' :
                    achievement.rarity === 'epic' ? 'text-purple-600' :
                    achievement.rarity === 'rare' ? 'text-blue-600' : 'text-gray-600'
                  }`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{achievement.name}</p>
                  <p className="text-xs text-gray-500">{achievement.description}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
