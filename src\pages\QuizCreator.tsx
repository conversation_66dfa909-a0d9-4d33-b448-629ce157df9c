import React, { useState } from 'react';
import { 
  ArrowLeft, 
  Upload, 
  Link, 
  Type, 
  FileText, 
  Image, 
  Mic, 
  Video,
  Brain,
  Settings,
  Wand2,
  CheckCircle,
  Plus,
  Trash2,
  Edit3
} from 'lucide-react';
import { generateQuizFromText } from '../services/ai-service';

interface QuizCreatorProps {
  onBack: () => void;
}

type ContentSource = 'text' | 'url' | 'file' | 'image' | 'audio' | 'video';
type QuestionType = 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';

interface QuizSettings {
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'adaptive';
  questionCount: number;
  timeLimit: number;
  questionTypes: QuestionType[];
  category: string;
  isPublic: boolean;
}

interface Question {
  id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  points: number;
  timeLimit: number;
}

const QuizCreator: React.FC<QuizCreatorProps> = ({ onBack }) => {
  const [step, setStep] = useState<'source' | 'settings' | 'questions' | 'preview'>('source');
  const [contentSource, setContentSource] = useState<ContentSource>('text');
  const [content, setContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  
  const [settings, setSettings] = useState<QuizSettings>({
    title: '',
    description: '',
    difficulty: 'intermediate',
    questionCount: 10,
    timeLimit: 30,
    questionTypes: ['multiple-choice'],
    category: 'General',
    isPublic: false
  });

  const contentSources = [
    {
      id: 'text' as ContentSource,
      name: 'Text Input',
      description: 'Paste or type your content directly',
      icon: Type,
      color: 'bg-blue-500'
    },
    {
      id: 'url' as ContentSource,
      name: 'Website URL',
      description: 'Extract content from any webpage',
      icon: Link,
      color: 'bg-green-500'
    },
    {
      id: 'file' as ContentSource,
      name: 'File Upload',
      description: 'Upload PDF, DOC, or TXT files',
      icon: FileText,
      color: 'bg-purple-500'
    },
    {
      id: 'image' as ContentSource,
      name: 'Image/Photo',
      description: 'Extract text from images using OCR',
      icon: Image,
      color: 'bg-orange-500'
    },
    {
      id: 'audio' as ContentSource,
      name: 'Audio Recording',
      description: 'Convert speech to text and generate quiz',
      icon: Mic,
      color: 'bg-red-500'
    },
    {
      id: 'video' as ContentSource,
      name: 'Video Content',
      description: 'Extract content from video transcripts',
      icon: Video,
      color: 'bg-indigo-500'
    }
  ];

  const questionTypes = [
    { id: 'multiple-choice', name: 'Multiple Choice', description: 'Traditional A, B, C, D questions' },
    { id: 'true-false', name: 'True/False', description: 'Simple binary choice questions' },
    { id: 'short-answer', name: 'Short Answer', description: 'Brief text responses' },
    { id: 'essay', name: 'Essay', description: 'Long-form written responses' }
  ];

  const categories = [
    'General', 'Science', 'Technology', 'Mathematics', 'History', 'Literature',
    'Programming', 'Business', 'Arts', 'Sports', 'Medicine', 'Law'
  ];

  const handleGenerateQuiz = async () => {
    if (!content.trim()) return;
    
    setIsGenerating(true);
    try {
      const generatedQuestions = await generateQuizFromText(content, settings);
      setQuestions(generatedQuestions);
      setStep('questions');
    } catch (error) {
      console.error('Error generating quiz:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveQuiz = () => {
    // Save quiz logic here
    console.log('Saving quiz:', { settings, questions });
    onBack();
  };

  const addNewQuestion = () => {
    const newQuestion: Question = {
      id: `q_${Date.now()}`,
      type: 'multiple-choice',
      question: '',
      options: ['', '', '', ''],
      correctAnswer: '',
      explanation: '',
      points: 1,
      timeLimit: 30
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (id: string, updates: Partial<Question>) => {
    setQuestions(questions.map(q => q.id === id ? { ...q, ...updates } : q));
  };

  const deleteQuestion = (id: string) => {
    setQuestions(questions.filter(q => q.id !== id));
  };

  const renderSourceSelection = () => (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Content Source</h2>
        <p className="text-gray-600">Select how you'd like to provide content for your quiz</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {contentSources.map((source) => {
          const Icon = source.icon;
          const isSelected = contentSource === source.id;
          return (
            <button
              key={source.id}
              onClick={() => setContentSource(source.id)}
              className={`p-6 rounded-xl border-2 transition-all duration-200 text-left ${
                isSelected
                  ? 'border-indigo-500 bg-indigo-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
              }`}
            >
              <div className={`w-12 h-12 ${source.color} rounded-lg flex items-center justify-center mb-4`}>
                <Icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">{source.name}</h3>
              <p className="text-sm text-gray-600">{source.description}</p>
            </button>
          );
        })}
      </div>

      {contentSource === 'text' && (
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Content Text
          </label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Paste your content here... This could be lecture notes, textbook chapters, articles, or any educational material you'd like to create a quiz from."
            className="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
          />
          <div className="mt-4 flex justify-between items-center">
            <p className="text-sm text-gray-500">
              {content.length} characters • Recommended: 500+ characters for better questions
            </p>
            <button
              onClick={() => setStep('settings')}
              disabled={content.length < 50}
              className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <span>Continue</span>
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {contentSource === 'url' && (
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Website URL
          </label>
          <input
            type="url"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="https://example.com/article"
            className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
          <p className="text-sm text-gray-500 mt-2">
            We'll extract the main content from this webpage automatically
          </p>
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => setStep('settings')}
              disabled={!content.includes('http')}
              className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <span>Continue</span>
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {contentSource !== 'text' && contentSource !== 'url' && (
        <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
          <Upload className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {contentSources.find(s => s.id === contentSource)?.name} Upload
          </h3>
          <p className="text-gray-600 mb-4">
            {contentSources.find(s => s.id === contentSource)?.description}
          </p>
          <button className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            Upload File
          </button>
          <p className="text-xs text-gray-500 mt-2">Coming soon in the next update!</p>
        </div>
      )}
    </div>
  );

  const renderSettings = () => (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Quiz Settings</h2>
        <p className="text-gray-600">Configure your quiz parameters</p>
      </div>

      <div className="bg-white rounded-xl border border-gray-200 p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Quiz Title</label>
            <input
              type="text"
              value={settings.title}
              onChange={(e) => setSettings({...settings, title: e.target.value})}
              placeholder="Enter quiz title"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <select
              value={settings.category}
              onChange={(e) => setSettings({...settings, category: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              {categories.map(cat => (
                <option key={cat} value={cat}>{cat}</option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
          <textarea
            value={settings.description}
            onChange={(e) => setSettings({...settings, description: e.target.value})}
            placeholder="Brief description of the quiz content"
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
            <select
              value={settings.difficulty}
              onChange={(e) => setSettings({...settings, difficulty: e.target.value as any})}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
              <option value="adaptive">Adaptive</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Questions</label>
            <input
              type="number"
              value={settings.questionCount}
              onChange={(e) => setSettings({...settings, questionCount: parseInt(e.target.value)})}
              min="5"
              max="50"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Time Limit (min)</label>
            <input
              type="number"
              value={settings.timeLimit}
              onChange={(e) => setSettings({...settings, timeLimit: parseInt(e.target.value)})}
              min="5"
              max="120"
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">Question Types</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {questionTypes.map((type) => (
              <label key={type.id} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.questionTypes.includes(type.id as QuestionType)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSettings({
                        ...settings,
                        questionTypes: [...settings.questionTypes, type.id as QuestionType]
                      });
                    } else {
                      setSettings({
                        ...settings,
                        questionTypes: settings.questionTypes.filter(t => t !== type.id)
                      });
                    }
                  }}
                  className="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <div>
                  <div className="font-medium text-gray-900">{type.name}</div>
                  <div className="text-sm text-gray-600">{type.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <button
            onClick={() => setStep('source')}
            className="px-6 py-2 text-gray-600 hover:text-gray-800 flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back</span>
          </button>
          <button
            onClick={handleGenerateQuiz}
            disabled={!settings.title || settings.questionTypes.length === 0 || isGenerating}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Generating...</span>
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4" />
                <span>Generate Quiz</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  const renderQuestions = () => (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8 flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Review & Edit Questions</h2>
          <p className="text-gray-600">Fine-tune your AI-generated questions</p>
        </div>
        <button
          onClick={addNewQuestion}
          className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Question</span>
        </button>
      </div>

      <div className="space-y-6">
        {questions.map((question, index) => (
          <div key={question.id} className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-2">
                <span className="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full text-sm font-medium">
                  Question {index + 1}
                </span>
                <select
                  value={question.type}
                  onChange={(e) => updateQuestion(question.id, { type: e.target.value as QuestionType })}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
                >
                  {questionTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </select>
              </div>
              <button
                onClick={() => deleteQuestion(question.id)}
                className="text-red-600 hover:text-red-700 p-1"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Question</label>
                <textarea
                  value={question.question}
                  onChange={(e) => updateQuestion(question.id, { question: e.target.value })}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  rows={2}
                />
              </div>

              {question.type === 'multiple-choice' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Options</label>
                  <div className="space-y-2">
                    {question.options?.map((option, optIndex) => (
                      <div key={optIndex} className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name={`correct-${question.id}`}
                          checked={question.correctAnswer === option}
                          onChange={() => updateQuestion(question.id, { correctAnswer: option })}
                          className="h-4 w-4 text-indigo-600"
                        />
                        <input
                          type="text"
                          value={option}
                          onChange={(e) => {
                            const newOptions = [...(question.options || [])];
                            newOptions[optIndex] = e.target.value;
                            updateQuestion(question.id, { options: newOptions });
                          }}
                          placeholder={`Option ${optIndex + 1}`}
                          className="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Explanation</label>
                <textarea
                  value={question.explanation}
                  onChange={(e) => updateQuestion(question.id, { explanation: e.target.value })}
                  placeholder="Explain why this answer is correct..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  rows={2}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 flex items-center justify-between">
        <button
          onClick={() => setStep('settings')}
          className="px-6 py-2 text-gray-600 hover:text-gray-800 flex items-center space-x-2"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </button>
        <div className="flex space-x-4">
          <button
            onClick={() => setStep('preview')}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            Preview
          </button>
          <button
            onClick={handleSaveQuiz}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2"
          >
            <CheckCircle className="w-4 h-4" />
            <span>Publish Quiz</span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-3">
              <Brain className="w-8 h-8 text-indigo-600" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Quiz Creator</h1>
                <p className="text-sm text-gray-600">AI-Powered Quiz Generation</p>
              </div>
            </div>
          </div>

          {/* Step Indicator */}
          <div className="hidden md:flex items-center space-x-4">
            {['source', 'settings', 'questions', 'preview'].map((stepName, index) => {
              const isActive = step === stepName;
              const isCompleted = ['source', 'settings', 'questions', 'preview'].indexOf(step) > index;
              return (
                <div key={stepName} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    isActive ? 'bg-indigo-600 text-white' :
                    isCompleted ? 'bg-green-600 text-white' :
                    'bg-gray-200 text-gray-600'
                  }`}>
                    {isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
                  </div>
                  {index < 3 && (
                    <div className={`w-12 h-0.5 ${
                      isCompleted ? 'bg-green-600' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {step === 'source' && renderSourceSelection()}
        {step === 'settings' && renderSettings()}
        {step === 'questions' && renderQuestions()}
        {step === 'preview' && (
          <div className="max-w-2xl mx-auto text-center py-12">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Quiz Preview</h2>
            <p className="text-gray-600 mb-8">Preview functionality coming soon!</p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setStep('questions')}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Back to Edit
              </button>
              <button
                onClick={handleSaveQuiz}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Publish Quiz
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default QuizCreator;