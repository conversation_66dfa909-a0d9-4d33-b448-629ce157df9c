/**
 * React Hook for Rate Limiting
 * Provides client-side rate limiting functionality for user actions
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { rateLimiter, RateLimitResult } from '../services/rate-limiter';
import { useAuthStore } from '../store/authStore';

interface UseRateLimitOptions {
  windowMs: number;
  maxRequests: number;
  message?: string;
  onLimitExceeded?: (result: RateLimitResult) => void;
}

interface UseRateLimitReturn {
  checkLimit: () => RateLimitResult;
  isAllowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  message?: string;
  executeWithLimit: <T>(action: () => Promise<T> | T) => Promise<T | null>;
}

/**
 * Custom hook for rate limiting user actions
 */
export const useRateLimit = (
  key: string,
  options: UseRateLimitOptions
): UseRateLimitReturn => {
  const { user } = useAuthStore();
  const [lastResult, setLastResult] = useState<RateLimitResult>({
    allowed: true,
    remaining: options.maxRequests,
    resetTime: Date.now() + options.windowMs
  });

  const optionsRef = useRef(options);
  optionsRef.current = options;

  const checkLimit = useCallback(() => {
    const identifier = user?.id || 'anonymous';
    const result = rateLimiter.checkLimit(
      key,
      optionsRef.current.windowMs,
      optionsRef.current.maxRequests,
      identifier
    );

    if (!result.allowed && optionsRef.current.message) {
      result.message = optionsRef.current.message;
    }

    setLastResult(result);

    if (!result.allowed && optionsRef.current.onLimitExceeded) {
      optionsRef.current.onLimitExceeded(result);
    }

    return result;
  }, [key, user?.id]);

  const executeWithLimit = useCallback(async <T>(
    action: () => Promise<T> | T
  ): Promise<T | null> => {
    const result = checkLimit();
    
    if (!result.allowed) {
      throw new Error(result.message || 'Rate limit exceeded');
    }

    try {
      return await action();
    } catch (error) {
      // Re-throw the error so it can be handled by the caller
      throw error;
    }
  }, [checkLimit]);

  return {
    checkLimit,
    isAllowed: lastResult.allowed,
    remaining: lastResult.remaining,
    resetTime: lastResult.resetTime,
    retryAfter: lastResult.retryAfter,
    message: lastResult.message,
    executeWithLimit
  };
};

/**
 * Hook for quiz creation rate limiting
 */
export const useQuizCreationRateLimit = () => {
  const { user } = useAuthStore();
  const [lastResult, setLastResult] = useState<RateLimitResult>({
    allowed: true,
    remaining: 10,
    resetTime: Date.now() + 60 * 60 * 1000
  });

  const checkLimit = useCallback(() => {
    if (!user?.id) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now(),
        message: 'Please log in to create quizzes'
      };
    }

    const result = rateLimiter.checkQuizCreationLimit(user.id);
    setLastResult(result);
    return result;
  }, [user?.id]);

  const executeWithLimit = useCallback(async <T>(
    action: () => Promise<T> | T
  ): Promise<T | null> => {
    const result = checkLimit();
    
    if (!result.allowed) {
      throw new Error(result.message || 'Quiz creation rate limit exceeded');
    }

    return await action();
  }, [checkLimit]);

  return {
    checkLimit,
    isAllowed: lastResult.allowed,
    remaining: lastResult.remaining,
    resetTime: lastResult.resetTime,
    retryAfter: lastResult.retryAfter,
    message: lastResult.message,
    executeWithLimit
  };
};

/**
 * Hook for quiz attempt rate limiting
 */
export const useQuizAttemptRateLimit = () => {
  const { user } = useAuthStore();
  const [lastResult, setLastResult] = useState<RateLimitResult>({
    allowed: true,
    remaining: 50,
    resetTime: Date.now() + 60 * 60 * 1000
  });

  const checkLimit = useCallback(() => {
    if (!user?.id) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now(),
        message: 'Please log in to take quizzes'
      };
    }

    const result = rateLimiter.checkQuizAttemptLimit(user.id);
    setLastResult(result);
    return result;
  }, [user?.id]);

  const executeWithLimit = useCallback(async <T>(
    action: () => Promise<T> | T
  ): Promise<T | null> => {
    const result = checkLimit();
    
    if (!result.allowed) {
      throw new Error(result.message || 'Quiz attempt rate limit exceeded');
    }

    return await action();
  }, [checkLimit]);

  return {
    checkLimit,
    isAllowed: lastResult.allowed,
    remaining: lastResult.remaining,
    resetTime: lastResult.resetTime,
    retryAfter: lastResult.retryAfter,
    message: lastResult.message,
    executeWithLimit
  };
};

/**
 * Hook for AI generation rate limiting
 */
export const useAiGenerationRateLimit = () => {
  const { user } = useAuthStore();
  const [lastResult, setLastResult] = useState<RateLimitResult>({
    allowed: true,
    remaining: 20,
    resetTime: Date.now() + 60 * 60 * 1000
  });

  const checkLimit = useCallback(() => {
    if (!user?.id) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now(),
        message: 'Please log in to use AI generation'
      };
    }

    const result = rateLimiter.checkAiGenerationLimit(user.id);
    setLastResult(result);
    return result;
  }, [user?.id]);

  const executeWithLimit = useCallback(async <T>(
    action: () => Promise<T> | T
  ): Promise<T | null> => {
    const result = checkLimit();
    
    if (!result.allowed) {
      throw new Error(result.message || 'AI generation rate limit exceeded');
    }

    return await action();
  }, [checkLimit]);

  return {
    checkLimit,
    isAllowed: lastResult.allowed,
    remaining: lastResult.remaining,
    resetTime: lastResult.resetTime,
    retryAfter: lastResult.retryAfter,
    message: lastResult.message,
    executeWithLimit
  };
};

/**
 * Hook for file upload rate limiting
 */
export const useUploadRateLimit = () => {
  const { user } = useAuthStore();
  const [lastResult, setLastResult] = useState<RateLimitResult>({
    allowed: true,
    remaining: 10,
    resetTime: Date.now() + 60 * 60 * 1000
  });

  const checkLimit = useCallback(() => {
    if (!user?.id) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: Date.now(),
        message: 'Please log in to upload files'
      };
    }

    const result = rateLimiter.checkUploadLimit(user.id);
    setLastResult(result);
    return result;
  }, [user?.id]);

  const executeWithLimit = useCallback(async <T>(
    action: () => Promise<T> | T
  ): Promise<T | null> => {
    const result = checkLimit();
    
    if (!result.allowed) {
      throw new Error(result.message || 'File upload rate limit exceeded');
    }

    return await action();
  }, [checkLimit]);

  return {
    checkLimit,
    isAllowed: lastResult.allowed,
    remaining: lastResult.remaining,
    resetTime: lastResult.resetTime,
    retryAfter: lastResult.retryAfter,
    message: lastResult.message,
    executeWithLimit
  };
};

/**
 * Utility function to format time remaining
 */
export const formatTimeRemaining = (resetTime: number): string => {
  const now = Date.now();
  const remaining = Math.max(0, resetTime - now);
  
  if (remaining === 0) return 'Now';
  
  const minutes = Math.floor(remaining / (60 * 1000));
  const seconds = Math.floor((remaining % (60 * 1000)) / 1000);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  
  return `${seconds}s`;
};
