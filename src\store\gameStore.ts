import { create } from 'zustand';

interface Player {
  id: string;
  name: string;
  avatar: string;
  score: number;
  isReady: boolean;
  isHost: boolean;
  isConnected: boolean;
  joinedAt: Date;
}

interface GameRoom {
  id: string;
  name: string;
  hostId: string;
  players: Player[];
  maxPlayers: number;
  quizId: string;
  quizTitle: string;
  status: 'waiting' | 'starting' | 'active' | 'paused' | 'finished';
  gameMode: 'speed' | 'accuracy' | 'survival';
  difficulty: 'easy' | 'medium' | 'hard';
  settings: {
    timePerQuestion: number;
    questionCount: number;
    isPrivate: boolean;
    allowSpectators: boolean;
  };
  currentQuestion?: number;
  createdAt: Date;
}

interface GameQuestion {
  id: string;
  question: string;
  options: string[];
  correctAnswer: string;
  timeLimit: number;
  points: number;
}

interface PlayerAnswer {
  playerId: string;
  questionId: string;
  answer: string;
  timeSpent: number;
  isCorrect: boolean;
  submittedAt: Date;
}

interface GameState {
  currentRoom: GameRoom | null;
  availableRooms: GameRoom[];
  currentQuestion: GameQuestion | null;
  playerAnswers: PlayerAnswer[];
  gameStartTime: Date | null;
  questionStartTime: Date | null;
  isConnected: boolean;
  connectionError: string | null;
  leaderboard: Player[];
}

interface GameActions {
  // Room management
  createRoom: (roomData: Partial<GameRoom>) => void;
  joinRoom: (roomId: string, player: Player) => void;
  leaveRoom: () => void;
  updateRoom: (updates: Partial<GameRoom>) => void;
  setAvailableRooms: (rooms: GameRoom[]) => void;
  
  // Player management
  addPlayer: (player: Player) => void;
  removePlayer: (playerId: string) => void;
  updatePlayer: (playerId: string, updates: Partial<Player>) => void;
  togglePlayerReady: (playerId: string) => void;
  
  // Game flow
  startGame: () => void;
  pauseGame: () => void;
  endGame: () => void;
  nextQuestion: (question: GameQuestion) => void;
  submitAnswer: (playerId: string, questionId: string, answer: string) => void;
  
  // Connection management
  setConnectionStatus: (connected: boolean) => void;
  setConnectionError: (error: string | null) => void;
  
  // Utilities
  updateLeaderboard: () => void;
  reset: () => void;
}

type GameStore = GameState & GameActions;

export const useGameStore = create<GameStore>((set, get) => ({
  // Initial state
  currentRoom: null,
  availableRooms: [],
  currentQuestion: null,
  playerAnswers: [],
  gameStartTime: null,
  questionStartTime: null,
  isConnected: false,
  connectionError: null,
  leaderboard: [],

  // Room management
  createRoom: (roomData: Partial<GameRoom>) => {
    const room: GameRoom = {
      id: `room_${Date.now()}`,
      name: roomData.name || 'New Game Room',
      hostId: roomData.hostId || '',
      players: roomData.players || [],
      maxPlayers: roomData.maxPlayers || 4,
      quizId: roomData.quizId || '',
      quizTitle: roomData.quizTitle || '',
      status: 'waiting',
      gameMode: roomData.gameMode || 'speed',
      difficulty: roomData.difficulty || 'medium',
      settings: {
        timePerQuestion: 30,
        questionCount: 15,
        isPrivate: false,
        allowSpectators: true,
        ...roomData.settings
      },
      createdAt: new Date()
    };
    
    set({ currentRoom: room });
  },

  joinRoom: (roomId: string, player: Player) => {
    set((state) => {
      const room = state.availableRooms.find(r => r.id === roomId) || state.currentRoom;
      if (!room || room.players.length >= room.maxPlayers) return state;

      const updatedRoom = {
        ...room,
        players: [...room.players, { ...player, joinedAt: new Date() }]
      };

      return {
        currentRoom: updatedRoom,
        availableRooms: state.availableRooms.map(r => 
          r.id === roomId ? updatedRoom : r
        )
      };
    });
  },

  leaveRoom: () => {
    set({
      currentRoom: null,
      currentQuestion: null,
      playerAnswers: [],
      gameStartTime: null,
      questionStartTime: null
    });
  },

  updateRoom: (updates: Partial<GameRoom>) => {
    set((state) => {
      if (!state.currentRoom) return state;
      
      const updatedRoom = { ...state.currentRoom, ...updates };
      
      return {
        currentRoom: updatedRoom,
        availableRooms: state.availableRooms.map(r => 
          r.id === updatedRoom.id ? updatedRoom : r
        )
      };
    });
  },

  setAvailableRooms: (rooms: GameRoom[]) => {
    set({ availableRooms: rooms });
  },

  // Player management
  addPlayer: (player: Player) => {
    set((state) => {
      if (!state.currentRoom) return state;

      return {
        currentRoom: {
          ...state.currentRoom,
          players: [...state.currentRoom.players, player]
        }
      };
    });
  },

  removePlayer: (playerId: string) => {
    set((state) => {
      if (!state.currentRoom) return state;

      const updatedPlayers = state.currentRoom.players.filter(p => p.id !== playerId);
      
      // If host leaves, assign new host
      let newHostId = state.currentRoom.hostId;
      if (playerId === state.currentRoom.hostId && updatedPlayers.length > 0) {
        newHostId = updatedPlayers[0].id;
        updatedPlayers[0].isHost = true;
      }

      return {
        currentRoom: {
          ...state.currentRoom,
          hostId: newHostId,
          players: updatedPlayers
        }
      };
    });
  },

  updatePlayer: (playerId: string, updates: Partial<Player>) => {
    set((state) => {
      if (!state.currentRoom) return state;

      return {
        currentRoom: {
          ...state.currentRoom,
          players: state.currentRoom.players.map(p =>
            p.id === playerId ? { ...p, ...updates } : p
          )
        }
      };
    });
  },

  togglePlayerReady: (playerId: string) => {
    get().updatePlayer(playerId, { 
      isReady: !get().currentRoom?.players.find(p => p.id === playerId)?.isReady 
    });
  },

  // Game flow
  startGame: () => {
    set((state) => ({
      gameStartTime: new Date(),
      currentRoom: state.currentRoom ? {
        ...state.currentRoom,
        status: 'active'
      } : null
    }));
  },

  pauseGame: () => {
    get().updateRoom({ status: 'paused' });
  },

  endGame: () => {
    set((state) => ({
      currentRoom: state.currentRoom ? {
        ...state.currentRoom,
        status: 'finished'
      } : null,
      currentQuestion: null,
      questionStartTime: null
    }));
    
    get().updateLeaderboard();
  },

  nextQuestion: (question: GameQuestion) => {
    set({
      currentQuestion: question,
      questionStartTime: new Date()
    });
  },

  submitAnswer: (playerId: string, questionId: string, answer: string) => {
    const currentTime = new Date();
    const questionStartTime = get().questionStartTime;
    const timeSpent = questionStartTime 
      ? currentTime.getTime() - questionStartTime.getTime() 
      : 0;

    const currentQuestion = get().currentQuestion;
    const isCorrect = currentQuestion ? answer === currentQuestion.correctAnswer : false;

    const playerAnswer: PlayerAnswer = {
      playerId,
      questionId,
      answer,
      timeSpent,
      isCorrect,
      submittedAt: currentTime
    };

    set((state) => ({
      playerAnswers: [...state.playerAnswers, playerAnswer]
    }));

    // Update player score
    if (isCorrect && currentQuestion) {
      const basePoints = currentQuestion.points;
      const timeBonus = Math.max(0, (currentQuestion.timeLimit - timeSpent / 1000) / currentQuestion.timeLimit);
      const finalPoints = Math.round(basePoints * (1 + timeBonus * 0.5));

      get().updatePlayer(playerId, {
        score: (get().currentRoom?.players.find(p => p.id === playerId)?.score || 0) + finalPoints
      });
    }
  },

  // Connection management
  setConnectionStatus: (connected: boolean) => {
    set({ isConnected: connected, connectionError: connected ? null : 'Connection lost' });
  },

  setConnectionError: (error: string | null) => {
    set({ connectionError: error, isConnected: !error });
  },

  // Utilities
  updateLeaderboard: () => {
    set((state) => {
      if (!state.currentRoom) return state;

      const sortedPlayers = [...state.currentRoom.players]
        .sort((a, b) => b.score - a.score);

      return { leaderboard: sortedPlayers };
    });
  },

  reset: () => {
    set({
      currentRoom: null,
      currentQuestion: null,
      playerAnswers: [],
      gameStartTime: null,
      questionStartTime: null,
      leaderboard: []
    });
  }
}));