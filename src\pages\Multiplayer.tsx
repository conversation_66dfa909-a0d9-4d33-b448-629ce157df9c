import React, { useState, useEffect } from 'react';
import {
  ArrowLeft,
  Users,
  Plus,
  Crown,
  Zap,
  Clock,
  Trophy,
  Target,
  Play,
  UserPlus,
  Copy,
  Check,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useAuthStore } from '../store/authStore';

interface MultiplayerProps {
  onBack: () => void;
}

interface Room {
  id: string;
  name: string;
  host: string;
  players: Player[];
  maxPlayers: number;
  quiz: string;
  status: 'waiting' | 'active' | 'finished';
  gameMode: 'speed' | 'accuracy' | 'survival';
  difficulty: 'easy' | 'medium' | 'hard';
  isPrivate: boolean;
}

interface Player {
  id: string;
  name: string;
  avatar: string;
  score: number;
  isReady: boolean;
  isHost: boolean;
}

const Multiplayer: React.FC<MultiplayerProps> = ({ onBack }) => {
  const { user } = useAuthStore();
  const [currentView, setCurrentView] = useState<'lobby' | 'room' | 'game' | 'results'>('lobby');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [roomCode, setRoomCode] = useState('');
  const [isConnected, setIsConnected] = useState(true);
  const [copied, setCopied] = useState(false);

  // Mock data for demo
  const [rooms, setRooms] = useState<Room[]>([
    {
      id: '1',
      name: 'JavaScript Masters',
      host: 'Sarah Chen',
      players: [
        { id: '1', name: 'Sarah Chen', avatar: 'https://images.pexels.com/photos/2380794/pexels-photo-2380794.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop', score: 0, isReady: true, isHost: true },
        { id: '2', name: 'Mike Johnson', avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop', score: 0, isReady: true, isHost: false },
        { id: '3', name: 'Emma Wilson', avatar: 'https://images.pexels.com/photos/2726111/pexels-photo-2726111.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop', score: 0, isReady: false, isHost: false }
      ],
      maxPlayers: 6,
      quiz: 'Advanced JavaScript Concepts',
      status: 'waiting',
      gameMode: 'speed',
      difficulty: 'hard',
      isPrivate: false
    },
    {
      id: '2',
      name: 'React Quick Battle',
      host: 'Alex Rodriguez',
      players: [
        { id: '4', name: 'Alex Rodriguez', avatar: 'https://images.pexels.com/photos/2613260/pexels-photo-2613260.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop', score: 0, isReady: true, isHost: true },
        { id: '5', name: 'Lisa Park', avatar: 'https://images.pexels.com/photos/2100063/pexels-photo-2100063.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop', score: 0, isReady: true, isHost: false }
      ],
      maxPlayers: 4,
      quiz: 'React Hooks Deep Dive',
      status: 'waiting',
      gameMode: 'accuracy',
      difficulty: 'medium',
      isPrivate: false
    },
    {
      id: '3',
      name: 'TypeScript Challenge',
      host: 'David Kim',
      players: [
        { id: '6', name: 'David Kim', avatar: 'https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop', score: 0, isReady: true, isHost: true }
      ],
      maxPlayers: 8,
      quiz: 'TypeScript Best Practices',
      status: 'waiting',
      gameMode: 'survival',
      difficulty: 'hard',
      isPrivate: true
    }
  ]);

  const [newRoom, setNewRoom] = useState({
    name: '',
    quiz: 'Advanced JavaScript Concepts',
    maxPlayers: 4,
    gameMode: 'speed' as const,
    difficulty: 'medium' as const,
    isPrivate: false
  });

  const gameModes = [
    { id: 'speed', name: 'Speed Challenge', description: 'Race against time', icon: Zap, color: 'text-yellow-600' },
    { id: 'accuracy', name: 'Accuracy Battle', description: 'Precision over speed', icon: Target, color: 'text-blue-600' },
    { id: 'survival', name: 'Last One Standing', description: 'Elimination style', icon: Trophy, color: 'text-red-600' }
  ];

  const quizzes = [
    'Advanced JavaScript Concepts',
    'React Hooks Deep Dive',
    'TypeScript Best Practices',
    'Node.js APIs',
    'Database Design',
    'System Architecture'
  ];

  useEffect(() => {
    // Simulate connection status
    const interval = setInterval(() => {
      setIsConnected(Math.random() > 0.1); // 90% uptime simulation
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const createRoom = () => {
    const room: Room = {
      id: Date.now().toString(),
      ...newRoom,
      host: user?.name || 'Anonymous',
      players: [{
        id: user?.id || '1',
        name: user?.name || 'Anonymous',
        avatar: user?.avatar || '',
        score: 0,
        isReady: true,
        isHost: true
      }],
      status: 'waiting'
    };
    setRooms([...rooms, room]);
    setSelectedRoom(room);
    setCurrentView('room');
    setIsCreatingRoom(false);
  };

  const joinRoom = (room: Room) => {
    const updatedRoom = {
      ...room,
      players: [
        ...room.players,
        {
          id: user?.id || Date.now().toString(),
          name: user?.name || 'Anonymous',
          avatar: user?.avatar || '',
          score: 0,
          isReady: false,
          isHost: false
        }
      ]
    };
    setSelectedRoom(updatedRoom);
    setCurrentView('room');
  };

  const copyRoomCode = () => {
    navigator.clipboard.writeText(selectedRoom?.id || '');
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const startGame = () => {
    setCurrentView('game');
    // Simulate game progression
    setTimeout(() => {
      setCurrentView('results');
    }, 30000); // 30 seconds for demo
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-700';
      case 'medium': return 'bg-yellow-100 text-yellow-700';
      case 'hard': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const renderLobby = () => (
    <div className="max-w-6xl mx-auto">
      {/* Connection Status */}
      <div className={`mb-6 p-4 rounded-lg border ${isConnected ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="w-5 h-5 text-green-600" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-600" />
          )}
          <span className={`text-sm font-medium ${isConnected ? 'text-green-700' : 'text-red-700'}`}>
            {isConnected ? 'Connected to multiplayer servers' : 'Connection unstable - retrying...'}
          </span>
        </div>
      </div>

      <div className="flex justify-between items-center mb-8">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Game Rooms</h2>
          <p className="text-gray-600">Join an active room or create your own challenge</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setIsCreatingRoom(true)}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>Create Room</span>
          </button>
        </div>
      </div>

      {/* Quick Join */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Join</h3>
        <div className="flex space-x-3">
          <input
            type="text"
            value={roomCode}
            onChange={(e) => setRoomCode(e.target.value)}
            placeholder="Enter room code..."
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          />
          <button
            disabled={!roomCode}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Join
          </button>
        </div>
      </div>

      {/* Available Rooms */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {rooms.filter(room => !room.isPrivate).map((room) => {
          const gameModeInfo = gameModes.find(mode => mode.id === room.gameMode);
          const GameModeIcon = gameModeInfo?.icon || Zap;
          
          return (
            <div key={room.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{room.name}</h3>
                    <p className="text-sm text-gray-600">{room.quiz}</p>
                  </div>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(room.difficulty)}`}>
                    {room.difficulty}
                  </span>
                </div>

                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center space-x-1">
                    <GameModeIcon className={`w-4 h-4 ${gameModeInfo?.color}`} />
                    <span className="text-sm text-gray-600">{gameModeInfo?.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">{room.players.length}/{room.maxPlayers}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mb-4">
                  {room.players.slice(0, 3).map((player) => (
                    <img
                      key={player.id}
                      src={player.avatar}
                      alt={player.name}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ))}
                  {room.players.length > 3 && (
                    <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-gray-600">+{room.players.length - 3}</span>
                    </div>
                  )}
                  <div className="ml-2">
                    <div className="flex items-center space-x-1">
                      <Crown className="w-3 h-3 text-yellow-500" />
                      <span className="text-xs text-gray-600">{room.host}</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => joinRoom(room)}
                  disabled={room.players.length >= room.maxPlayers || room.status !== 'waiting'}
                  className="w-full px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium"
                >
                  {room.status === 'active' ? 'Game in Progress' :
                   room.players.length >= room.maxPlayers ? 'Room Full' : 'Join Room'}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Create Room Modal */}
      {isCreatingRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <h3 className="text-xl font-semibold text-gray-900 mb-6">Create New Room</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Room Name</label>
                <input
                  type="text"
                  value={newRoom.name}
                  onChange={(e) => setNewRoom({...newRoom, name: e.target.value})}
                  placeholder="Enter room name..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Quiz Topic</label>
                <select
                  value={newRoom.quiz}
                  onChange={(e) => setNewRoom({...newRoom, quiz: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                >
                  {quizzes.map(quiz => (
                    <option key={quiz} value={quiz}>{quiz}</option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Max Players</label>
                  <select
                    value={newRoom.maxPlayers}
                    onChange={(e) => setNewRoom({...newRoom, maxPlayers: parseInt(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value={2}>2 Players</option>
                    <option value={4}>4 Players</option>
                    <option value={6}>6 Players</option>
                    <option value={8}>8 Players</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Difficulty</label>
                  <select
                    value={newRoom.difficulty}
                    onChange={(e) => setNewRoom({...newRoom, difficulty: e.target.value as any})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="easy">Easy</option>
                    <option value="medium">Medium</option>
                    <option value="hard">Hard</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Game Mode</label>
                <div className="space-y-2">
                  {gameModes.map((mode) => {
                    const Icon = mode.icon;
                    return (
                      <label key={mode.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input
                          type="radio"
                          name="gameMode"
                          value={mode.id}
                          checked={newRoom.gameMode === mode.id}
                          onChange={(e) => setNewRoom({...newRoom, gameMode: e.target.value as any})}
                          className="h-4 w-4 text-indigo-600"
                        />
                        <Icon className={`w-5 h-5 ${mode.color}`} />
                        <div>
                          <div className="font-medium text-gray-900">{mode.name}</div>
                          <div className="text-sm text-gray-600">{mode.description}</div>
                        </div>
                      </label>
                    );
                  })}
                </div>
              </div>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={newRoom.isPrivate}
                  onChange={(e) => setNewRoom({...newRoom, isPrivate: e.target.checked})}
                  className="h-4 w-4 text-indigo-600"
                />
                <span className="text-sm text-gray-700">Private room (invite only)</span>
              </label>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setIsCreatingRoom(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={createRoom}
                disabled={!newRoom.name}
                className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create Room
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  const renderRoom = () => (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedRoom?.name}</h2>
            <p className="text-gray-600">{selectedRoom?.quiz}</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={copyRoomCode}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 flex items-center space-x-2"
            >
              {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              <span>Room Code: {selectedRoom?.id}</span>
            </button>
            <button
              onClick={startGame}
              disabled={selectedRoom?.players.some(p => !p.isReady)}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <Play className="w-4 h-4" />
              <span>Start Game</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Players ({selectedRoom?.players.length}/{selectedRoom?.maxPlayers})</h3>
            <div className="space-y-3">
              {selectedRoom?.players.map((player) => (
                <div key={player.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <img
                      src={player.avatar}
                      alt={player.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900">{player.name}</span>
                        {player.isHost && <Crown className="w-4 h-4 text-yellow-500" />}
                      </div>
                      <span className={`text-sm ${player.isReady ? 'text-green-600' : 'text-orange-600'}`}>
                        {player.isReady ? 'Ready' : 'Not Ready'}
                      </span>
                    </div>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${player.isReady ? 'bg-green-500' : 'bg-orange-500'}`} />
                </div>
              ))}
            </div>

            <button className="w-full mt-4 px-4 py-2 border-2 border-dashed border-gray-300 text-gray-500 rounded-lg hover:border-gray-400 hover:text-gray-600 flex items-center justify-center space-x-2">
              <UserPlus className="w-4 h-4" />
              <span>Invite Friends</span>
            </button>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Game Settings</h3>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Game Mode</span>
                  <span className="text-sm text-gray-900">{gameModes.find(m => m.id === selectedRoom?.gameMode)?.name}</span>
                </div>
                <p className="text-xs text-gray-600">{gameModes.find(m => m.id === selectedRoom?.gameMode)?.description}</p>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Difficulty</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(selectedRoom?.difficulty || 'medium')}`}>
                    {selectedRoom?.difficulty}
                  </span>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Questions</span>
                  <span className="text-sm text-gray-900">15 questions</span>
                </div>
              </div>

              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Time Limit</span>
                  <span className="text-sm text-gray-900">30 seconds per question</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderGame = () => (
    <div className="max-w-3xl mx-auto">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
        <div className="mb-8">
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Zap className="w-8 h-8 text-indigo-600" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Game in Progress</h2>
          <p className="text-gray-600">Multiplayer quiz functionality coming soon!</p>
        </div>

        <div className="grid grid-cols-3 gap-6 mb-8">
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-600">15</div>
            <div className="text-sm text-gray-600">Questions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">30s</div>
            <div className="text-sm text-gray-600">Per Question</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{selectedRoom?.players.length || 0}</div>
            <div className="text-sm text-gray-600">Players</div>
          </div>
        </div>

        <div className="animate-pulse bg-gray-200 h-2 rounded-full mb-4">
          <div className="bg-indigo-600 h-2 rounded-full w-1/3 transition-all duration-300" />
        </div>

        <p className="text-sm text-gray-500">This feature will include real-time questions, live scoring, and interactive gameplay!</p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={currentView === 'lobby' ? onBack : () => setCurrentView('lobby')}
              className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-3">
              <Users className="w-8 h-8 text-indigo-600" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Multiplayer Arena</h1>
                <p className="text-sm text-gray-600">Compete with players worldwide</p>
              </div>
            </div>
          </div>

          {currentView === 'room' && (
            <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>{selectedRoom?.players.length}/{selectedRoom?.maxPlayers} players</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>Waiting to start</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {currentView === 'lobby' && renderLobby()}
        {currentView === 'room' && renderRoom()}
        {currentView === 'game' && renderGame()}
        {currentView === 'results' && (
          <div className="max-w-2xl mx-auto text-center">
            <Trophy className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Game Results</h2>
            <p className="text-gray-600 mb-8">Results and leaderboard functionality coming soon!</p>
            <button
              onClick={() => setCurrentView('lobby')}
              className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700"
            >
              Back to Lobby
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Multiplayer;