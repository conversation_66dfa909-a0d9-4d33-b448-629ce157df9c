# QuizCraft AI - Production-Ready Multi-Stage Dockerfile

# =============================================================================
# Stage 1: Dependencies
# =============================================================================
FROM node:18-alpine AS dependencies

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install dependencies with cache optimization
RUN npm ci --only=production --silent && \
    npm cache clean --force

# =============================================================================
# Stage 2: Build
# =============================================================================
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy dependencies from previous stage
COPY --from=dependencies /app/node_modules ./node_modules

# Copy source code
COPY . .

# Set build environment variables
ENV NODE_ENV=production
ENV VITE_APP_VERSION=1.0.0
ENV VITE_BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Build the application
RUN npm run build

# Optimize build output
RUN npm run build:analyze || true

# =============================================================================
# Stage 3: Production
# =============================================================================
FROM nginx:alpine AS production

# Install additional tools
RUN apk add --no-cache \
    curl \
    jq \
    bash

# Create nginx user and directories
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Copy built application
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# Copy SSL certificates (if available)
COPY ssl/ /etc/ssl/certs/ 2>/dev/null || true

# Create health check endpoint
RUN echo '<!DOCTYPE html><html><head><title>Health Check</title></head><body><h1>OK</h1><p>QuizCraft AI is running</p></body></html>' > /usr/share/nginx/html/health

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Add health check script
COPY scripts/health-check.sh /usr/local/bin/health-check.sh
RUN chmod +x /usr/local/bin/health-check.sh

# Expose ports
EXPOSE 80 443

# Add labels for metadata
LABEL maintainer="QuizCraft AI Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="QuizCraft AI - Intelligent Learning Platform"
LABEL org.opencontainers.image.title="QuizCraft AI"
LABEL org.opencontainers.image.description="Production-ready quiz application with AI-powered features"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="QuizCraft AI"
LABEL org.opencontainers.image.licenses="MIT"

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# Start nginx
CMD ["nginx", "-g", "daemon off;"]

# =============================================================================
# Stage 4: Development (Optional)
# =============================================================================
FROM node:18-alpine AS development

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    bash

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install all dependencies (including dev)
RUN npm install

# Copy source code
COPY . .

# Expose development port
EXPOSE 5173

# Set development environment
ENV NODE_ENV=development
ENV VITE_HMR_PORT=5173

# Start development server
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

# =============================================================================
# Stage 5: Testing
# =============================================================================
FROM node:18-alpine AS testing

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl \
    bash \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Set Puppeteer environment
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Copy package files
COPY package*.json ./
COPY yarn.lock* ./

# Install dependencies
RUN npm ci

# Copy source code
COPY . .

# Run tests
CMD ["npm", "run", "test:ci"]
