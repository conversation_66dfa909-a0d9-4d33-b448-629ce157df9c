/**
 * API Service for handling HTTP requests and external integrations
 */

// Base configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';
const TIMEOUT_MS = 10000;

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
}

class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  private async request<T>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = TIMEOUT_MS
    } = options;

    const url = `${this.baseUrl}${endpoint}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const config: RequestInit = {
        method,
        headers: { ...this.defaultHeaders, ...headers },
        signal: controller.signal,
      };

      if (body && method !== 'GET') {
        config.body = typeof body === 'string' ? body : JSON.stringify(body);
      }

      const response = await fetch(url, config);
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return { success: true, data };

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          return { success: false, error: 'Request timeout' };
        }
        return { success: false, error: error.message };
      }
      
      return { success: false, error: 'Unknown error occurred' };
    }
  }

  // Authentication methods
  async login(email: string, password: string) {
    return this.request<{ user: any; token: string }>('/auth/login', {
      method: 'POST',
      body: { email, password }
    });
  }

  async register(userData: any) {
    return this.request<{ user: any; token: string }>('/auth/register', {
      method: 'POST',
      body: userData
    });
  }

  async logout() {
    return this.request('/auth/logout', { method: 'POST' });
  }

  // Quiz methods
  async getQuizzes(filters?: any) {
    const queryParams = filters ? new URLSearchParams(filters).toString() : '';
    return this.request<any[]>(`/quizzes${queryParams ? `?${queryParams}` : ''}`);
  }

  async getQuizById(id: string) {
    return this.request<any>(`/quizzes/${id}`);
  }

  async createQuiz(quizData: any) {
    return this.request<any>('/quizzes', {
      method: 'POST',
      body: quizData
    });
  }

  async updateQuiz(id: string, updates: any) {
    return this.request<any>(`/quizzes/${id}`, {
      method: 'PUT',
      body: updates
    });
  }

  async deleteQuiz(id: string) {
    return this.request(`/quizzes/${id}`, { method: 'DELETE' });
  }

  // Quiz attempts
  async submitQuizAttempt(attemptData: any) {
    return this.request<any>('/quiz-attempts', {
      method: 'POST',
      body: attemptData
    });
  }

  async getUserAttempts(userId: string) {
    return this.request<any[]>(`/users/${userId}/attempts`);
  }

  // Analytics
  async getUserAnalytics(userId: string, timeRange?: string) {
    const params = timeRange ? `?timeRange=${timeRange}` : '';
    return this.request<any>(`/analytics/users/${userId}${params}`);
  }

  async getQuizAnalytics(quizId: string) {
    return this.request<any>(`/analytics/quizzes/${quizId}`);
  }

  // Multiplayer
  async createGameRoom(roomData: any) {
    return this.request<any>('/multiplayer/rooms', {
      method: 'POST',
      body: roomData
    });
  }

  async getGameRooms() {
    return this.request<any[]>('/multiplayer/rooms');
  }

  async joinGameRoom(roomId: string, playerId: string) {
    return this.request<any>(`/multiplayer/rooms/${roomId}/join`, {
      method: 'POST',
      body: { playerId }
    });
  }

  // Content extraction
  async extractContentFromUrl(url: string) {
    return this.request<{ content: string; title: string }>('/content/extract', {
      method: 'POST',
      body: { url }
    });
  }

  async processFileUpload(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    return fetch(`${this.baseUrl}/content/upload`, {
      method: 'POST',
      body: formData,
    }).then(response => response.json());
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// External API integrations
export class ExternalApiService {
  // Wikipedia API for content enhancement
  static async searchWikipedia(query: string): Promise<any> {
    try {
      const response = await fetch(
        `https://en.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(query)}`,
        {
          headers: {
            'User-Agent': 'QuizCraft/1.0 (https://quizcraft.ai; <EMAIL>)'
          }
        }
      );

      if (!response.ok) {
        throw new Error('Wikipedia API request failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Wikipedia API error:', error);
      return null;
    }
  }

  // Free translation API (using LibreTranslate or similar)
  static async translateText(text: string, targetLang: string = 'en'): Promise<string | null> {
    try {
      // Mock implementation - replace with actual free translation service
      const response = await fetch('https://libretranslate.de/translate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          source: 'auto',
          target: targetLang,
          format: 'text'
        })
      });

      if (!response.ok) {
        throw new Error('Translation request failed');
      }

      const data = await response.json();
      return data.translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      return null;
    }
  }

  // Free OCR API for image text extraction
  static async extractTextFromImage(imageFile: File): Promise<string | null> {
    try {
      // Mock implementation - in production, use OCR.space free tier or similar
      const formData = new FormData();
      formData.append('file', imageFile);
      formData.append('language', 'eng');
      formData.append('isOverlayRequired', 'false');

      const response = await fetch('https://api.ocr.space/parse/image', {
        method: 'POST',
        headers: {
          'apikey': 'FREE_OCR_API_KEY' // Replace with actual free API key
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error('OCR request failed');
      }

      const data = await response.json();
      return data.ParsedResults?.[0]?.ParsedText || null;
    } catch (error) {
      console.error('OCR error:', error);
      return null;
    }
  }

  // Free text-to-speech API
  static async textToSpeech(text: string): Promise<string | null> {
    try {
      // Using Web Speech API (browser native)
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        speechSynthesis.speak(utterance);
        return 'success';
      }

      // Fallback to free TTS service
      const response = await fetch('https://api.voicerss.org/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          key: 'FREE_TTS_API_KEY', // Replace with actual free API key
          src: text,
          hl: 'en-us',
          f: '44khz_16bit_stereo',
          c: 'mp3'
        })
      });

      if (!response.ok) {
        throw new Error('TTS request failed');
      }

      const audioBlob = await response.blob();
      return URL.createObjectURL(audioBlob);
    } catch (error) {
      console.error('TTS error:', error);
      return null;
    }
  }
}

// Error handling utilities
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Request interceptors for common functionality
export const setupApiInterceptors = () => {
  // Add request/response interceptors here if needed
  console.log('API interceptors setup complete');
};

// Cache management for offline support
export class ApiCache {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  static set(key: string, data: any, ttlMs: number = 5 * 60 * 1000) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }

  static get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  static clear() {
    this.cache.clear();
  }

  static has(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }
}

export default apiClient;