import React, { useState } from 'react';
import {
  <PERSON>L<PERSON>t,
  TrendingUp,
  Target,
  Clock,
  Award,
  Brain,
  BarChart3,
  Calendar,
  Filter,
  Download
} from 'lucide-react';

interface AnalyticsProps {
  onBack: () => void;
}

const Analytics: React.FC<AnalyticsProps> = ({ onBack }) => {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const performanceData = {
    totalQuizzes: 47,
    averageScore: 87.3,
    totalTime: 1240, // minutes
    streak: 12,
    improvement: 15.2,
    accuracyTrend: [78, 82, 85, 87, 89, 87, 91],
    speedTrend: [45, 42, 40, 38, 35, 36, 34], // seconds per question
    categoryPerformance: [
      { name: 'JavaScript', score: 92, quizzes: 12, improvement: 8 },
      { name: 'React', score: 89, quizzes: 8, improvement: 12 },
      { name: 'TypeScript', score: 85, quizzes: 6, improvement: 5 },
      { name: 'Node.js', score: 81, quizzes: 9, improvement: 18 },
      { name: 'Database', score: 78, quizzes: 5, improvement: -3 },
      { name: 'System Design', score: 76, quizzes: 7, improvement: 22 }
    ],
    recentActivity: [
      { date: '2024-01-15', quiz: 'Advanced JavaScript', score: 94, time: 18 },
      { date: '2024-01-14', quiz: 'React Hooks', score: 87, time: 22 },
      { date: '2024-01-13', quiz: 'TypeScript Basics', score: 91, time: 15 },
      { date: '2024-01-12', quiz: 'Node.js APIs', score: 83, time: 25 },
      { date: '2024-01-11', quiz: 'Database Design', score: 78, time: 30 }
    ],
    weaknesses: [
      { topic: 'Async/Await Patterns', accuracy: 65, recommendation: 'Practice Promise chaining' },
      { topic: 'Database Indexing', accuracy: 58, recommendation: 'Study index optimization' },
      { topic: 'System Scalability', accuracy: 62, recommendation: 'Learn load balancing concepts' }
    ]
  };

  const StatCard = ({ title, value, change, icon: Icon, color }: any) => (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <p className={`text-sm mt-1 flex items-center ${
              change > 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              <TrendingUp className={`w-4 h-4 mr-1 ${change < 0 ? 'rotate-180' : ''}`} />
              {Math.abs(change)}% from last month
            </p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </div>
  );

  const ProgressChart = ({ data, label, color }: any) => (
    <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{label}</h3>
      <div className="h-48 flex items-end justify-between space-x-1">
        {data.map((value: number, index: number) => (
          <div key={index} className="flex-1 flex flex-col items-center">
            <div
              className={`w-full ${color} rounded-t transition-all duration-300 hover:opacity-80`}
              style={{ height: `${(value / Math.max(...data)) * 100}%`, minHeight: '4px' }}
            />
            <span className="text-xs text-gray-500 mt-2">{value}</span>
          </div>
        ))}
      </div>
      <div className="flex justify-between text-xs text-gray-500 mt-2">
        <span>7 days ago</span>
        <span>Today</span>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-3">
              <BarChart3 className="w-8 h-8 text-indigo-600" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Learning Analytics</h1>
                <p className="text-sm text-gray-600">Track your progress and identify areas for improvement</p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 3 months</option>
              <option value="1y">Last year</option>
            </select>
            <button className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100">
              <Download className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 max-w-7xl mx-auto">
        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Total Quizzes"
            value={performanceData.totalQuizzes}
            change={12}
            icon={Brain}
            color="bg-blue-500"
          />
          <StatCard
            title="Average Score"
            value={`${performanceData.averageScore}%`}
            change={performanceData.improvement}
            icon={Target}
            color="bg-green-500"
          />
          <StatCard
            title="Study Time"
            value={`${Math.round(performanceData.totalTime / 60)}h`}
            change={8}
            icon={Clock}
            color="bg-purple-500"
          />
          <StatCard
            title="Current Streak"
            value={`${performanceData.streak} days`}
            change={null}
            icon={Award}
            color="bg-orange-500"
          />
        </div>

        {/* Charts Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <ProgressChart
            data={performanceData.accuracyTrend}
            label="Accuracy Trend (%)"
            color="bg-blue-500"
          />
          <ProgressChart
            data={performanceData.speedTrend}
            label="Average Time per Question (seconds)"
            color="bg-purple-500"
          />
        </div>

        {/* Category Performance */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Performance by Category</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {performanceData.categoryPerformance.map((category, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{category.name}</h4>
                        <div className="flex items-center space-x-4">
                          <span className="text-sm text-gray-600">{category.quizzes} quizzes</span>
                          <span className={`text-sm font-medium ${
                            category.improvement > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {category.improvement > 0 ? '+' : ''}{category.improvement}%
                          </span>
                          <span className="text-lg font-semibold text-gray-900">{category.score}%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${category.score}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Weakness Analysis */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-lg font-semibold text-gray-900">Areas for Improvement</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {performanceData.weaknesses.map((weakness, index) => (
                  <div key={index} className="p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{weakness.topic}</h4>
                      <span className="text-sm font-medium text-red-600">{weakness.accuracy}%</span>
                    </div>
                    <p className="text-sm text-gray-600">{weakness.recommendation}</p>
                    <div className="mt-2 w-full bg-red-200 rounded-full h-1.5">
                      <div
                        className="bg-red-600 h-1.5 rounded-full"
                        style={{ width: `${weakness.accuracy}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
              <button className="text-indigo-600 hover:text-indigo-700 text-sm font-medium">
                View All
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left">
                    <th className="pb-3 text-sm font-medium text-gray-600">Date</th>
                    <th className="pb-3 text-sm font-medium text-gray-600">Quiz</th>
                    <th className="pb-3 text-sm font-medium text-gray-600">Score</th>
                    <th className="pb-3 text-sm font-medium text-gray-600">Time</th>
                    <th className="pb-3 text-sm font-medium text-gray-600">Performance</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  {performanceData.recentActivity.map((activity, index) => (
                    <tr key={index} className="py-3">
                      <td className="py-3 text-sm text-gray-600">
                        {new Date(activity.date).toLocaleDateString()}
                      </td>
                      <td className="py-3 text-sm font-medium text-gray-900">{activity.quiz}</td>
                      <td className="py-3">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          activity.score >= 90 ? 'bg-green-100 text-green-700' :
                          activity.score >= 70 ? 'bg-yellow-100 text-yellow-700' :
                          'bg-red-100 text-red-700'
                        }`}>
                          {activity.score}%
                        </span>
                      </td>
                      <td className="py-3 text-sm text-gray-600">{activity.time} min</td>
                      <td className="py-3">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-1.5 mr-2">
                            <div
                              className={`h-1.5 rounded-full ${
                                activity.score >= 90 ? 'bg-green-500' :
                                activity.score >= 70 ? 'bg-yellow-500' :
                                'bg-red-500'
                              }`}
                              style={{ width: `${activity.score}%` }}
                            />
                          </div>
                          <span className="text-xs text-gray-600">{activity.score}%</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* AI Insights */}
        <div className="mt-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl p-6 text-white">
          <div className="flex items-start space-x-4">
            <div className="p-2 bg-white bg-opacity-20 rounded-lg">
              <Brain className="w-6 h-6" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">AI Learning Insights</h3>
              <p className="text-indigo-100 mb-4">
                Based on your recent performance, here are personalized recommendations:
              </p>
              <div className="space-y-2">
                <p className="text-sm">
                  • Your JavaScript skills are excellent! Consider advancing to frameworks like Vue.js
                </p>
                <p className="text-sm">
                  • Focus on database optimization - your weakest area with 78% average
                </p>
                <p className="text-sm">
                  • Your learning pace is optimal at 2.3 questions per minute
                </p>
                <p className="text-sm">
                  • System design concepts need attention - schedule 30 min daily practice
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;