/**
 * Rate Limiting Service
 * Implements comprehensive rate limiting for API endpoints, user actions, and anonymous users
 */

import { RATE_LIMIT_CONFIG } from '../utils/constants';

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
  message?: string;
}

class RateLimiter {
  private storage: Map<string, RateLimitEntry> = new Map();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Check if a request is allowed based on rate limiting rules
   */
  checkLimit(
    key: string,
    windowMs: number,
    maxRequests: number,
    identifier?: string
  ): RateLimitResult {
    const now = Date.now();
    const limitKey = identifier ? `${key}:${identifier}` : key;
    const entry = this.storage.get(limitKey);

    if (!entry) {
      // First request
      this.storage.set(limitKey, {
        count: 1,
        resetTime: now + windowMs,
        firstRequest: now
      });

      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: now + windowMs
      };
    }

    // Check if window has expired
    if (now >= entry.resetTime) {
      // Reset the window
      this.storage.set(limitKey, {
        count: 1,
        resetTime: now + windowMs,
        firstRequest: now
      });

      return {
        allowed: true,
        remaining: maxRequests - 1,
        resetTime: now + windowMs
      };
    }

    // Check if limit exceeded
    if (entry.count >= maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil((entry.resetTime - now) / 1000)
      };
    }

    // Increment count
    entry.count++;
    this.storage.set(limitKey, entry);

    return {
      allowed: true,
      remaining: maxRequests - entry.count,
      resetTime: entry.resetTime
    };
  }

  /**
   * Check API endpoint rate limit
   */
  checkApiLimit(ip: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.api;
    return this.checkLimit('api', config.windowMs, config.maxRequests, ip);
  }

  /**
   * Check quiz creation rate limit
   */
  checkQuizCreationLimit(userId: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.quiz.creation;
    const result = this.checkLimit('quiz:creation', config.windowMs, config.maxRequests, userId);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check quiz attempt rate limit
   */
  checkQuizAttemptLimit(userId: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.quiz.attempts;
    const result = this.checkLimit('quiz:attempts', config.windowMs, config.maxRequests, userId);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check AI generation rate limit
   */
  checkAiGenerationLimit(userId: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.quiz.generation;
    const result = this.checkLimit('quiz:generation', config.windowMs, config.maxRequests, userId);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check user login rate limit
   */
  checkLoginLimit(ip: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.user.login;
    const result = this.checkLimit('user:login', config.windowMs, config.maxRequests, ip);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check user registration rate limit
   */
  checkRegistrationLimit(ip: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.user.registration;
    const result = this.checkLimit('user:registration', config.windowMs, config.maxRequests, ip);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check password reset rate limit
   */
  checkPasswordResetLimit(email: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.user.passwordReset;
    const result = this.checkLimit('user:password-reset', config.windowMs, config.maxRequests, email);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check anonymous user rate limit
   */
  checkAnonymousLimit(ip: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.anonymous;
    const result = this.checkLimit('anonymous', config.windowMs, config.maxRequests, ip);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check file upload rate limit
   */
  checkUploadLimit(userId: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.upload;
    const result = this.checkLimit('upload', config.windowMs, config.maxRequests, userId);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check multiplayer room creation rate limit
   */
  checkRoomCreationLimit(userId: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.multiplayer.roomCreation;
    const result = this.checkLimit('multiplayer:room-creation', config.windowMs, config.maxRequests, userId);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Check multiplayer game actions rate limit
   */
  checkGameActionsLimit(userId: string): RateLimitResult {
    const config = RATE_LIMIT_CONFIG.multiplayer.gameActions;
    const result = this.checkLimit('multiplayer:game-actions', config.windowMs, config.maxRequests, userId);
    
    if (!result.allowed) {
      result.message = config.message;
    }
    
    return result;
  }

  /**
   * Reset rate limit for a specific key and identifier
   */
  resetLimit(key: string, identifier?: string): void {
    const limitKey = identifier ? `${key}:${identifier}` : key;
    this.storage.delete(limitKey);
  }

  /**
   * Get current rate limit status
   */
  getStatus(key: string, identifier?: string): RateLimitEntry | null {
    const limitKey = identifier ? `${key}:${identifier}` : key;
    return this.storage.get(limitKey) || null;
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.storage.entries()) {
      if (now >= entry.resetTime) {
        this.storage.delete(key);
      }
    }
  }

  /**
   * Get rate limit headers for HTTP responses
   */
  getHeaders(result: RateLimitResult, maxRequests: number): Record<string, string> {
    const headers: Record<string, string> = {
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': result.remaining.toString(),
      'X-RateLimit-Reset': Math.ceil(result.resetTime / 1000).toString()
    };

    if (result.retryAfter) {
      headers['Retry-After'] = result.retryAfter.toString();
    }

    return headers;
  }

  /**
   * Destroy the rate limiter and clean up resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.storage.clear();
  }
}

// Create singleton instance
export const rateLimiter = new RateLimiter();

// Export types
export type { RateLimitResult, RateLimitEntry };
