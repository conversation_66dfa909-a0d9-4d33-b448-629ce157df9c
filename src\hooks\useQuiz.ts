import { useCallback } from 'react';
import { useQuizStore } from '../store/quizStore';
import { generateQuizFromText } from '../services/ai-service';

export const useQuiz = () => {
  const {
    quizzes,
    currentQuiz,
    currentAttempt,
    userAttempts,
    isLoading,
    error,
    setQuizzes,
    addQuiz,
    updateQuiz,
    deleteQuiz,
    setCurrentQuiz,
    startQuizAttempt,
    submitAnswer,
    completeQuizAttempt,
    setLoading,
    setError,
    getUserAttempts,
    getQuizById
  } = useQuizStore();

  const createQuiz = useCallback(async (quizData: any) => {
    setLoading(true);
    setError(null);
    
    try {
      const newQuiz = {
        id: `quiz_${Date.now()}`,
        ...quizData,
        createdAt: new Date(),
        updatedAt: new Date(),
        stats: {
          totalAttempts: 0,
          averageScore: 0,
          averageTime: 0,
          passRate: 0,
          popularityScore: 0
        }
      };
      
      addQuiz(newQuiz);
      return newQuiz;
    } catch (error) {
      setError('Failed to create quiz');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [addQuiz, setLoading, setError]);

  const generateQuiz = useCallback(async (content: string, settings: any) => {
    setLoading(true);
    setError(null);
    
    try {
      const questions = await generateQuizFromText(content, settings);
      
      const quiz = {
        id: `quiz_${Date.now()}`,
        title: settings.title,
        description: settings.description,
        category: settings.category,
        difficulty: settings.difficulty,
        questions,
        timeLimit: settings.timeLimit,
        isPublic: !settings.isPrivate,
        createdBy: 'current_user',
        createdAt: new Date(),
        updatedAt: new Date(),
        stats: {
          totalAttempts: 0,
          averageScore: 0,
          averageTime: 0,
          passRate: 0,
          popularityScore: 0
        },
        tags: [],
        settings: {
          randomizeQuestions: false,
          randomizeOptions: false,
          showResults: true,
          allowRetakes: true,
          passScore: 70,
          certificateEnabled: false
        }
      };
      
      addQuiz(quiz);
      return quiz;
    } catch (error) {
      setError('Failed to generate quiz');
      throw error;
    } finally {
      setLoading(false);
    }
  }, [addQuiz, setLoading, setError]);

  const takeQuiz = useCallback((quizId: string, userId: string) => {
    const quiz = getQuizById(quizId);
    if (!quiz) {
      setError('Quiz not found');
      return;
    }
    
    setCurrentQuiz(quiz);
    startQuizAttempt(quizId, userId);
  }, [getQuizById, setCurrentQuiz, startQuizAttempt, setError]);

  const submitQuizAnswer = useCallback((questionId: string, answer: string) => {
    submitAnswer(questionId, answer);
  }, [submitAnswer]);

  const finishQuiz = useCallback(() => {
    completeQuizAttempt();
    setCurrentQuiz(null);
  }, [completeQuizAttempt, setCurrentQuiz]);

  return {
    // State
    quizzes,
    currentQuiz,
    currentAttempt,
    userAttempts,
    isLoading,
    error,
    
    // Actions
    createQuiz,
    generateQuiz,
    takeQuiz,
    submitQuizAnswer,
    finishQuiz,
    updateQuiz,
    deleteQuiz,
    getUserAttempts,
    getQuizById,
    setError
  };
};