/**
 * Custom Algorithm Implementations
 * Advanced algorithms for adaptive learning, spaced repetition, and intelligent recommendations
 */

import { numberUtils, dateUtils } from './helpers';

// Spaced Repetition Algorithm (SM-2 Algorithm)
export class SpacedRepetitionEngine {
  /**
   * Calculate next review date based on SM-2 algorithm
   * @param quality - User's response quality (0-5)
   * @param repetitions - Number of consecutive correct responses
   * @param easiness - Current easiness factor
   * @param interval - Current interval in days
   */
  static calculateNextReview(
    quality: number,
    repetitions: number,
    easiness: number,
    interval: number
  ): { nextReviewDate: Date; newEasiness: number; newInterval: number; newRepetitions: number } {
    let newEasiness = easiness;
    let newInterval = interval;
    let newRepetitions = repetitions;

    if (quality >= 3) {
      // Correct response
      if (repetitions === 0) {
        newInterval = 1;
      } else if (repetitions === 1) {
        newInterval = 6;
      } else {
        newInterval = Math.round(interval * easiness);
      }
      newRepetitions = repetitions + 1;
    } else {
      // Incorrect response - reset repetitions
      newRepetitions = 0;
      newInterval = 1;
    }

    // Update easiness factor
    newEasiness = Math.max(1.3, easiness + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02)));

    const nextReviewDate = dateUtils.addDays(new Date(), newInterval);

    return {
      nextReviewDate,
      newEasiness,
      newInterval,
      newRepetitions
    };
  }

  /**
   * Determine if a concept should be reviewed today
   */
  static shouldReview(lastReviewDate: Date, nextReviewDate: Date): boolean {
    const today = new Date();
    return today >= nextReviewDate;
  }

  /**
   * Calculate retention probability based on time elapsed
   */
  static calculateRetentionProbability(
    daysSinceReview: number,
    easiness: number,
    repetitions: number
  ): number {
    const stability = Math.pow(easiness, repetitions);
    const forgettingCurve = Math.exp(-daysSinceReview / stability);
    return Math.max(0, Math.min(1, forgettingCurve));
  }
}

// Adaptive Difficulty Algorithm
export class AdaptiveDifficultyEngine {
  private static readonly DIFFICULTY_LEVELS = ['easy', 'medium', 'hard'] as const;
  private static readonly TARGET_ACCURACY = 0.75; // 75% target accuracy
  private static readonly ADJUSTMENT_SENSITIVITY = 0.1;

  /**
   * Calculate optimal difficulty based on user performance
   */
  static calculateOptimalDifficulty(
    recentPerformance: Array<{ accuracy: number; difficulty: string; timestamp: Date }>
  ): string {
    if (recentPerformance.length === 0) return 'medium';

    // Weight recent performance more heavily
    const weightedPerformance = recentPerformance.map((perf, index) => ({
      ...perf,
      weight: Math.pow(0.9, recentPerformance.length - index - 1)
    }));

    const totalWeight = weightedPerformance.reduce((sum, perf) => sum + perf.weight, 0);
    const weightedAccuracy = weightedPerformance.reduce(
      (sum, perf) => sum + (perf.accuracy * perf.weight),
      0
    ) / totalWeight;

    const currentDifficultyIndex = this.DIFFICULTY_LEVELS.indexOf(
      recentPerformance[recentPerformance.length - 1].difficulty as any
    );

    let newDifficultyIndex = currentDifficultyIndex;

    if (weightedAccuracy > this.TARGET_ACCURACY + this.ADJUSTMENT_SENSITIVITY) {
      // User is performing too well, increase difficulty
      newDifficultyIndex = Math.min(this.DIFFICULTY_LEVELS.length - 1, currentDifficultyIndex + 1);
    } else if (weightedAccuracy < this.TARGET_ACCURACY - this.ADJUSTMENT_SENSITIVITY) {
      // User is struggling, decrease difficulty
      newDifficultyIndex = Math.max(0, currentDifficultyIndex - 1);
    }

    return this.DIFFICULTY_LEVELS[newDifficultyIndex];
  }

  /**
   * Adjust question parameters based on difficulty
   */
  static adjustQuestionParameters(
    baseDifficulty: string,
    userSkillLevel: number
  ): { timeMultiplier: number; pointsMultiplier: number; hintsAllowed: number } {
    const skillAdjustment = (userSkillLevel - 0.5) * 0.3; // -0.15 to +0.15

    const baseParameters = {
      easy: { timeMultiplier: 1.5, pointsMultiplier: 0.8, hintsAllowed: 3 },
      medium: { timeMultiplier: 1.0, pointsMultiplier: 1.0, hintsAllowed: 2 },
      hard: { timeMultiplier: 0.7, pointsMultiplier: 1.3, hintsAllowed: 1 }
    };

    const base = baseParameters[baseDifficulty as keyof typeof baseParameters] || baseParameters.medium;

    return {
      timeMultiplier: Math.max(0.5, base.timeMultiplier + skillAdjustment),
      pointsMultiplier: Math.max(0.5, base.pointsMultiplier - skillAdjustment),
      hintsAllowed: base.hintsAllowed
    };
  }
}

// Knowledge Graph and Learning Path Algorithm
export class LearningPathEngine {
  /**
   * Build knowledge graph from quiz data
   */
  static buildKnowledgeGraph(
    topics: string[],
    prerequisites: Record<string, string[]>,
    userMastery: Record<string, number>
  ): Map<string, { prerequisites: string[]; dependents: string[]; mastery: number }> {
    const graph = new Map();

    // Initialize nodes
    topics.forEach(topic => {
      graph.set(topic, {
        prerequisites: prerequisites[topic] || [],
        dependents: [],
        mastery: userMastery[topic] || 0
      });
    });

    // Build dependency relationships
    topics.forEach(topic => {
      const prereqs = prerequisites[topic] || [];
      prereqs.forEach(prereq => {
        if (graph.has(prereq)) {
          graph.get(prereq).dependents.push(topic);
        }
      });
    });

    return graph;
  }

  /**
   * Generate optimal learning path using topological sort
   */
  static generateLearningPath(
    knowledgeGraph: Map<string, any>,
    targetTopics: string[]
  ): string[] {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const path: string[] = [];

    const visit = (topic: string): void => {
      if (visiting.has(topic)) {
        throw new Error(`Circular dependency detected involving ${topic}`);
      }
      if (visited.has(topic)) return;

      visiting.add(topic);
      const node = knowledgeGraph.get(topic);
      
      if (node) {
        // Visit prerequisites first
        node.prerequisites.forEach((prereq: string) => {
          if (knowledgeGraph.has(prereq)) {
            visit(prereq);
          }
        });
      }

      visiting.delete(topic);
      visited.add(topic);
      path.push(topic);
    };

    // Visit all target topics
    targetTopics.forEach(topic => {
      if (knowledgeGraph.has(topic)) {
        visit(topic);
      }
    });

    return path;
  }

  /**
   * Recommend next topics based on current mastery
   */
  static recommendNextTopics(
    knowledgeGraph: Map<string, any>,
    masteryThreshold: number = 0.7
  ): Array<{ topic: string; priority: number; reason: string }> {
    const recommendations: Array<{ topic: string; priority: number; reason: string }> = [];

    knowledgeGraph.forEach((node, topic) => {
      if (node.mastery < masteryThreshold) {
        // Check if prerequisites are met
        const prerequisitesMet = node.prerequisites.every((prereq: string) => {
          const prereqNode = knowledgeGraph.get(prereq);
          return prereqNode && prereqNode.mastery >= masteryThreshold;
        });

        if (prerequisitesMet) {
          let priority = 1 - node.mastery; // Base priority on current mastery gap
          
          // Boost priority if this topic unlocks many others
          const dependentCount = node.dependents.length;
          priority += dependentCount * 0.1;

          // Boost priority if dependents are close to being unlocked
          const nearlyUnlockedDependents = node.dependents.filter((dependent: string) => {
            const depNode = knowledgeGraph.get(dependent);
            if (!depNode) return false;
            
            const otherPrereqsMet = depNode.prerequisites.filter((p: string) => p !== topic)
              .every((prereq: string) => {
                const prereqNode = knowledgeGraph.get(prereq);
                return prereqNode && prereqNode.mastery >= masteryThreshold;
              });
            
            return otherPrereqsMet;
          }).length;

          priority += nearlyUnlockedDependents * 0.2;

          let reason = `Current mastery: ${Math.round(node.mastery * 100)}%`;
          if (dependentCount > 0) {
            reason += `, unlocks ${dependentCount} topic(s)`;
          }

          recommendations.push({ topic, priority, reason });
        }
      }
    });

    return recommendations.sort((a, b) => b.priority - a.priority);
  }
}

// Collaborative Filtering for Recommendations
export class RecommendationEngine {
  /**
   * Calculate user similarity using cosine similarity
   */
  static calculateUserSimilarity(
    user1Ratings: Record<string, number>,
    user2Ratings: Record<string, number>
  ): number {
    const commonItems = Object.keys(user1Ratings).filter(item => 
      item in user2Ratings
    );

    if (commonItems.length === 0) return 0;

    let dotProduct = 0;
    let norm1 = 0;
    let norm2 = 0;

    commonItems.forEach(item => {
      const rating1 = user1Ratings[item];
      const rating2 = user2Ratings[item];
      
      dotProduct += rating1 * rating2;
      norm1 += rating1 * rating1;
      norm2 += rating2 * rating2;
    });

    if (norm1 === 0 || norm2 === 0) return 0;

    return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
  }

  /**
   * Generate quiz recommendations using collaborative filtering
   */
  static generateQuizRecommendations(
    targetUserId: string,
    userRatings: Record<string, Record<string, number>>, // userId -> quizId -> rating
    quizMetadata: Record<string, { category: string; difficulty: string; tags: string[] }>,
    maxRecommendations: number = 10
  ): Array<{ quizId: string; score: number; reason: string }> {
    const targetUserRatings = userRatings[targetUserId] || {};
    const recommendations = new Map<string, { score: number; reasons: string[] }>();

    // Find similar users
    const userSimilarities: Array<{ userId: string; similarity: number }> = [];
    
    Object.keys(userRatings).forEach(userId => {
      if (userId !== targetUserId) {
        const similarity = this.calculateUserSimilarity(
          targetUserRatings,
          userRatings[userId]
        );
        if (similarity > 0.1) { // Minimum similarity threshold
          userSimilarities.push({ userId, similarity });
        }
      }
    });

    // Sort by similarity
    userSimilarities.sort((a, b) => b.similarity - a.similarity);

    // Generate recommendations from similar users
    userSimilarities.slice(0, 20).forEach(({ userId, similarity }) => {
      const similarUserRatings = userRatings[userId];
      
      Object.keys(similarUserRatings).forEach(quizId => {
        if (!(quizId in targetUserRatings) && similarUserRatings[quizId] >= 4) {
          const currentRec = recommendations.get(quizId) || { score: 0, reasons: [] };
          const contribution = similarity * similarUserRatings[quizId];
          
          currentRec.score += contribution;
          currentRec.reasons.push(`Users with similar preferences rated this ${similarUserRatings[quizId]}/5`);
          
          recommendations.set(quizId, currentRec);
        }
      });
    });

    // Content-based filtering boost
    const targetUserCategories = this.getUserPreferredCategories(targetUserRatings, quizMetadata);
    
    recommendations.forEach((rec, quizId) => {
      const quiz = quizMetadata[quizId];
      if (quiz && targetUserCategories.includes(quiz.category)) {
        rec.score *= 1.2; // 20% boost for preferred categories
        rec.reasons.push(`Matches your interest in ${quiz.category}`);
      }
    });

    // Convert to array and sort
    const sortedRecommendations = Array.from(recommendations.entries())
      .map(([quizId, { score, reasons }]) => ({
        quizId,
        score,
        reason: reasons.join(', ')
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, maxRecommendations);

    return sortedRecommendations;
  }

  /**
   * Get user's preferred categories based on ratings
   */
  private static getUserPreferredCategories(
    userRatings: Record<string, number>,
    quizMetadata: Record<string, { category: string; difficulty: string; tags: string[] }>
  ): string[] {
    const categoryScores: Record<string, { total: number; count: number }> = {};

    Object.keys(userRatings).forEach(quizId => {
      const quiz = quizMetadata[quizId];
      const rating = userRatings[quizId];
      
      if (quiz && rating >= 4) { // Only consider highly rated quizzes
        if (!categoryScores[quiz.category]) {
          categoryScores[quiz.category] = { total: 0, count: 0 };
        }
        categoryScores[quiz.category].total += rating;
        categoryScores[quiz.category].count += 1;
      }
    });

    return Object.keys(categoryScores)
      .map(category => ({
        category,
        avgRating: categoryScores[category].total / categoryScores[category].count
      }))
      .filter(item => item.avgRating >= 4)
      .sort((a, b) => b.avgRating - a.avgRating)
      .map(item => item.category);
  }
}

// Performance Analytics Algorithm
export class PerformanceAnalyzer {
  /**
   * Calculate learning velocity (improvement rate over time)
   */
  static calculateLearningVelocity(
    performanceHistory: Array<{ score: number; timestamp: Date }>
  ): number {
    if (performanceHistory.length < 2) return 0;

    const sortedHistory = performanceHistory.sort((a, b) => 
      a.timestamp.getTime() - b.timestamp.getTime()
    );

    // Use linear regression to find trend
    const n = sortedHistory.length;
    let sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;

    sortedHistory.forEach((point, index) => {
      const x = index; // Time index
      const y = point.score;
      
      sumX += x;
      sumY += y;
      sumXY += x * y;
      sumXX += x * x;
    });

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return slope; // Points per quiz attempt
  }

  /**
   * Identify knowledge gaps using error pattern analysis
   */
  static identifyKnowledgeGaps(
    questionHistory: Array<{
      questionId: string;
      topic: string;
      difficulty: string;
      isCorrect: boolean;
      timeSpent: number;
    }>
  ): Array<{ topic: string; gapSeverity: number; confidence: number }> {
    const topicStats: Record<string, {
      correct: number;
      total: number;
      avgTimeSpent: number;
      difficultyDistribution: Record<string, number>;
    }> = {};

    // Aggregate statistics by topic
    questionHistory.forEach(question => {
      if (!topicStats[question.topic]) {
        topicStats[question.topic] = {
          correct: 0,
          total: 0,
          avgTimeSpent: 0,
          difficultyDistribution: {}
        };
      }

      const stats = topicStats[question.topic];
      stats.total += 1;
      if (question.isCorrect) stats.correct += 1;
      stats.avgTimeSpent += question.timeSpent;
      
      stats.difficultyDistribution[question.difficulty] = 
        (stats.difficultyDistribution[question.difficulty] || 0) + 1;
    });

    // Calculate gaps
    return Object.keys(topicStats).map(topic => {
      const stats = topicStats[topic];
      const accuracy = stats.correct / stats.total;
      const avgTime = stats.avgTimeSpent / stats.total;
      
      // Gap severity based on accuracy and time spent
      let gapSeverity = 1 - accuracy;
      
      // Adjust for time spent (longer time might indicate struggle)
      const expectedTime = 30; // seconds
      if (avgTime > expectedTime) {
        gapSeverity *= (1 + (avgTime - expectedTime) / expectedTime * 0.2);
      }
      
      // Confidence based on sample size
      const confidence = Math.min(1, stats.total / 10); // Full confidence at 10+ questions
      
      return {
        topic,
        gapSeverity: Math.min(1, gapSeverity),
        confidence
      };
    }).sort((a, b) => b.gapSeverity - a.gapSeverity);
  }

  /**
   * Predict future performance using exponential smoothing
   */
  static predictPerformance(
    recentScores: number[],
    alpha: number = 0.3
  ): { prediction: number; confidence: number } {
    if (recentScores.length === 0) return { prediction: 0, confidence: 0 };
    if (recentScores.length === 1) return { prediction: recentScores[0], confidence: 0.5 };

    let smoothedValue = recentScores[0];
    
    for (let i = 1; i < recentScores.length; i++) {
      smoothedValue = alpha * recentScores[i] + (1 - alpha) * smoothedValue;
    }

    // Calculate confidence based on variance
    const mean = numberUtils.average(recentScores);
    const variance = recentScores.reduce((sum, score) => 
      sum + Math.pow(score - mean, 2), 0
    ) / recentScores.length;
    
    const confidence = Math.max(0, 1 - variance / 1000); // Normalize variance

    return {
      prediction: smoothedValue,
      confidence: Math.min(1, confidence)
    };
  }
}

// Export all algorithms
export const algorithms = {
  spacedRepetition: SpacedRepetitionEngine,
  adaptiveDifficulty: AdaptiveDifficultyEngine,
  learningPath: LearningPathEngine,
  recommendation: RecommendationEngine,
  performance: PerformanceAnalyzer
};

export default algorithms;