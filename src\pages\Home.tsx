/**
 * Home Page Component
 * Landing page for QuizCraft AI with hero section, features, and call-to-action
 */

import React from 'react';
import { 
  Brain, 
  Users, 
  BarChart3, 
  Zap, 
  Shield, 
  Trophy,
  ArrowRight,
  Play,
  Star,
  CheckCircle
} from 'lucide-react';
import { useAuthStore } from '../store/authStore';

const Home: React.FC = () => {
  const { isAuthenticated, login } = useAuthStore();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      // Navigate to dashboard (in a real app, this would use router)
      window.location.hash = '#dashboard';
    } else {
      // Auto-login demo user for development
      login({
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
        role: 'student',
        stats: {
          totalQuizzes: 47,
          averageScore: 87,
          streak: 12,
          rank: 156
        }
      });
    }
  };

  const features = [
    {
      icon: Brain,
      title: 'AI-Powered Quiz Generation',
      description: 'Create intelligent quizzes automatically from any content using advanced AI technology.'
    },
    {
      icon: Users,
      title: 'Real-time Multiplayer',
      description: 'Compete with friends and colleagues in live quiz competitions and team challenges.'
    },
    {
      icon: BarChart3,
      title: 'Advanced Analytics',
      description: 'Track your learning progress with detailed insights and personalized recommendations.'
    },
    {
      icon: Zap,
      title: 'Adaptive Learning',
      description: 'Experience personalized difficulty adjustment based on your performance and learning style.'
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Built with enterprise-grade security features and comprehensive data protection.'
    },
    {
      icon: Trophy,
      title: 'Gamified Experience',
      description: 'Earn achievements, maintain streaks, and climb leaderboards while learning.'
    }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Education Director',
      company: 'TechEdu Inc.',
      content: 'QuizCraft AI has revolutionized how we create and deliver assessments. The AI generation saves us hours of work.',
      rating: 5
    },
    {
      name: 'Michael Chen',
      role: 'Training Manager',
      company: 'Global Corp',
      content: 'The analytics and adaptive learning features have significantly improved our training outcomes.',
      rating: 5
    },
    {
      name: 'Emily Rodriguez',
      role: 'Teacher',
      company: 'Lincoln High School',
      content: 'My students love the multiplayer quizzes. Engagement has increased by 300% since we started using QuizCraft.',
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                QuizCraft AI
              </span>
              <br />
              <span className="text-3xl md:text-5xl">
                Intelligent Learning Platform
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Revolutionize your learning experience with AI-powered quiz generation, 
              real-time multiplayer competitions, and adaptive learning technology.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <button
                onClick={handleGetStarted}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                <Play className="w-5 h-5" />
                Get Started Free
                <ArrowRight className="w-5 h-5" />
              </button>
              
              <button className="border-2 border-indigo-600 text-indigo-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-indigo-50 transition-all duration-200">
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-indigo-600">10K+</div>
                <div className="text-gray-600">Active Users</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">500K+</div>
                <div className="text-gray-600">Quizzes Created</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">95%</div>
                <div className="text-gray-600">Satisfaction Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">24/7</div>
                <div className="text-gray-600">Support</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features for Modern Learning
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the advanced capabilities that make QuizCraft AI the leading platform for intelligent learning and assessment.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-indigo-200">
                <div className="bg-gradient-to-r from-indigo-500 to-purple-500 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                  <feature.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Trusted by Educators Worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See what our users are saying about QuizCraft AI
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-lg">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-6 italic">
                  "{testimonial.content}"
                </p>
                <div>
                  <div className="font-semibold text-gray-900">{testimonial.name}</div>
                  <div className="text-sm text-gray-500">{testimonial.role}</div>
                  <div className="text-sm text-indigo-600">{testimonial.company}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-20 bg-gradient-to-r from-indigo-600 to-purple-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Learning Experience?
          </h2>
          <p className="text-xl text-indigo-100 mb-8">
            Join thousands of educators and learners who are already using QuizCraft AI to achieve better results.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleGetStarted}
              className="bg-white text-indigo-600 px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all duration-200 flex items-center justify-center gap-2 shadow-lg"
            >
              <CheckCircle className="w-5 h-5" />
              Start Free Trial
            </button>
            <button className="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-white hover:text-indigo-600 transition-all duration-200">
              Contact Sales
            </button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Brain className="w-8 h-8 text-indigo-400" />
            <span className="text-2xl font-bold">QuizCraft AI</span>
          </div>
          <p className="text-gray-400 mb-4">
            Intelligent Learning Platform for the Modern World
          </p>
          <p className="text-sm text-gray-500">
            © 2024 QuizCraft AI. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Home;
