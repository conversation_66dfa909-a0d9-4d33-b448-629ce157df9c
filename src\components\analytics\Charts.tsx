/**
 * Analytics Chart Components
 * Provides various chart types for displaying analytics data
 */

import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  RadialBarChart,
  RadialBar
} from 'recharts';
import { COLORS } from '../../utils/constants';

interface ChartProps {
  data: any[];
  height?: number;
  className?: string;
}

interface LineChartProps extends ChartProps {
  xKey: string;
  yKey: string;
  lineColor?: string;
  showGrid?: boolean;
  showDots?: boolean;
}

interface AreaChartProps extends ChartProps {
  xKey: string;
  yKey: string;
  fillColor?: string;
  strokeColor?: string;
  showGrid?: boolean;
}

interface BarChartProps extends ChartProps {
  xKey: string;
  yKey: string;
  barColor?: string;
  showGrid?: boolean;
}

interface PieChartProps extends ChartProps {
  dataKey: string;
  nameKey: string;
  colors?: string[];
  showLabels?: boolean;
}

interface RadialChartProps extends ChartProps {
  dataKey: string;
  nameKey: string;
  colors?: string[];
}

// Custom tooltip component
const CustomTooltip = ({ active, payload, label, formatter }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm font-medium text-gray-900">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {formatter ? formatter(entry.value) : entry.value}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Line Chart Component
export const AnalyticsLineChart: React.FC<LineChartProps> = ({
  data,
  xKey,
  yKey,
  lineColor = COLORS.primary[500],
  height = 300,
  showGrid = true,
  showDots = true,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
          <XAxis 
            dataKey={xKey} 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <Tooltip content={<CustomTooltip />} />
          <Line
            type="monotone"
            dataKey={yKey}
            stroke={lineColor}
            strokeWidth={2}
            dot={showDots ? { fill: lineColor, strokeWidth: 2, r: 4 } : false}
            activeDot={{ r: 6, fill: lineColor }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

// Area Chart Component
export const AnalyticsAreaChart: React.FC<AreaChartProps> = ({
  data,
  xKey,
  yKey,
  fillColor = COLORS.primary[500],
  strokeColor = COLORS.primary[600],
  height = 300,
  showGrid = true,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
          <XAxis 
            dataKey={xKey} 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey={yKey}
            stroke={strokeColor}
            fill={fillColor}
            fillOpacity={0.3}
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart Component
export const AnalyticsBarChart: React.FC<BarChartProps> = ({
  data,
  xKey,
  yKey,
  barColor = COLORS.primary[500],
  height = 300,
  showGrid = true,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />}
          <XAxis 
            dataKey={xKey} 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <YAxis 
            tick={{ fontSize: 12 }}
            stroke="#666"
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey={yKey} fill={barColor} radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart Component
export const AnalyticsPieChart: React.FC<PieChartProps> = ({
  data,
  dataKey,
  nameKey,
  colors = [
    COLORS.primary[500],
    COLORS.success[500],
    COLORS.warning[500],
    COLORS.error[500],
    '#8B5CF6',
    '#F59E0B'
  ],
  showLabels = true,
  height = 300,
  className = ''
}) => {
  const renderLabel = (entry: any) => {
    if (!showLabels) return '';
    return `${entry[nameKey]}: ${entry[dataKey]}`;
  };

  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey={dataKey}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Radial Bar Chart Component
export const AnalyticsRadialChart: React.FC<RadialChartProps> = ({
  data,
  dataKey,
  nameKey,
  colors = [COLORS.primary[500], COLORS.success[500], COLORS.warning[500]],
  height = 300,
  className = ''
}) => {
  return (
    <div className={`w-full ${className}`}>
      <ResponsiveContainer width="100%" height={height}>
        <RadialBarChart cx="50%" cy="50%" innerRadius="10%" outerRadius="80%" data={data}>
          <RadialBar
            minAngle={15}
            label={{ position: 'insideStart', fill: '#fff' }}
            background
            clockWise
            dataKey={dataKey}
          />
          <Legend iconSize={18} layout="vertical" verticalAlign="middle" align="right" />
          <Tooltip content={<CustomTooltip />} />
        </RadialBarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Progress Chart Component (for showing completion rates, etc.)
interface ProgressChartProps {
  value: number;
  max: number;
  label: string;
  color?: string;
  size?: number;
  strokeWidth?: number;
  className?: string;
}

export const ProgressChart: React.FC<ProgressChartProps> = ({
  value,
  max,
  label,
  color = COLORS.primary[500],
  size = 120,
  strokeWidth = 8,
  className = ''
}) => {
  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="relative" style={{ width: size, height: size }}>
        <svg
          className="transform -rotate-90"
          width={size}
          height={size}
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#e5e7eb"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={color}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={strokeDasharray}
            strokeDashoffset={strokeDashoffset}
            strokeLinecap="round"
            className="transition-all duration-300 ease-in-out"
          />
        </svg>
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              {Math.round(percentage)}%
            </div>
            <div className="text-xs text-gray-500">
              {value}/{max}
            </div>
          </div>
        </div>
      </div>
      <div className="mt-2 text-sm font-medium text-gray-700 text-center">
        {label}
      </div>
    </div>
  );
};
