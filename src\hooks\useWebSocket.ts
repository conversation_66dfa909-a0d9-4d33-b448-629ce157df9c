import { useEffect, useRef, useCallback } from 'react';
import { WebSocketService } from '../services/websocket';

type EventCallback = (data: any) => void;
type EventType = 'room_created' | 'room_updated' | 'player_joined' | 'player_left' | 
                'game_started' | 'question_started' | 'answer_submitted' | 'game_ended' |
                'notification' | 'user_status' | 'chat_message';

export const useWebSocket = (url?: string) => {
  const wsRef = useRef<WebSocketService | null>(null);
  const listenersRef = useRef<Map<EventType, Set<EventCallback>>>(new Map());

  useEffect(() => {
    if (!url) return;

    // Create WebSocket service instance
    wsRef.current = new WebSocketService({
      url,
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      heartbeatInterval: 30000
    });

    // Connect to WebSocket
    wsRef.current.connect().catch(console.error);

    // Cleanup on unmount
    return () => {
      if (wsRef.current) {
        wsRef.current.disconnect();
        wsRef.current = null;
      }
      listenersRef.current.clear();
    };
  }, [url]);

  const subscribe = useCallback((eventType: EventType, callback: EventCallback) => {
    if (!wsRef.current) return () => {};

    // Add to local listeners map
    if (!listenersRef.current.has(eventType)) {
      listenersRef.current.set(eventType, new Set());
    }
    listenersRef.current.get(eventType)!.add(callback);

    // Subscribe to WebSocket service
    wsRef.current.on(eventType, callback);

    // Return unsubscribe function
    return () => {
      if (wsRef.current) {
        wsRef.current.off(eventType, callback);
      }
      const listeners = listenersRef.current.get(eventType);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          listenersRef.current.delete(eventType);
        }
      }
    };
  }, []);

  const send = useCallback((eventType: EventType, data: any) => {
    if (wsRef.current && wsRef.current.isConnected()) {
      wsRef.current.send(eventType, data);
    } else {
      console.warn('WebSocket not connected, message queued:', eventType, data);
    }
  }, []);

  const isConnected = useCallback(() => {
    return wsRef.current ? wsRef.current.isConnected() : false;
  }, []);

  const getConnectionState = useCallback(() => {
    return wsRef.current ? wsRef.current.getConnectionState() : 'disconnected';
  }, []);

  return {
    subscribe,
    send,
    isConnected,
    getConnectionState
  };
};

// Specialized hook for multiplayer features
export const useMultiplayerWebSocket = () => {
  const wsUrl = import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001/multiplayer';
  const { subscribe, send, isConnected, getConnectionState } = useWebSocket(wsUrl);

  const joinRoom = useCallback((roomId: string, playerId: string) => {
    send('join_room', { roomId, playerId });
  }, [send]);

  const leaveRoom = useCallback((roomId: string, playerId: string) => {
    send('leave_room', { roomId, playerId });
  }, [send]);

  const startGame = useCallback((roomId: string) => {
    send('start_game', { roomId });
  }, [send]);

  const submitAnswer = useCallback((roomId: string, playerId: string, questionId: string, answer: string) => {
    send('answer_submitted', {
      roomId,
      playerId,
      questionId,
      answer,
      timestamp: Date.now()
    });
  }, [send]);

  const sendChatMessage = useCallback((roomId: string, playerId: string, message: string) => {
    send('chat_message', {
      roomId,
      playerId,
      message,
      timestamp: Date.now()
    });
  }, [send]);

  return {
    subscribe,
    joinRoom,
    leaveRoom,
    startGame,
    submitAnswer,
    sendChatMessage,
    isConnected,
    getConnectionState
  };
};