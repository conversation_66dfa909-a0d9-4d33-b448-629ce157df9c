import { create } from 'zustand';

interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  points: number;
  timeLimit?: number;
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
}

interface Quiz {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'adaptive';
  questions: Question[];
  timeLimit: number;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  stats: {
    totalAttempts: number;
    averageScore: number;
    averageTime: number;
  };
  tags: string[];
}

interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  answers: Record<string, string>;
  score: number;
  maxScore: number;
  percentage: number;
  timeSpent: number;
  completedAt: Date;
  isCompleted: boolean;
}

interface QuizState {
  quizzes: Quiz[];
  currentQuiz: Quiz | null;
  currentAttempt: QuizAttempt | null;
  userAttempts: QuizAttempt[];
  isLoading: boolean;
  error: string | null;
}

interface QuizActions {
  setQuizzes: (quizzes: Quiz[]) => void;
  addQuiz: (quiz: Quiz) => void;
  updateQuiz: (id: string, updates: Partial<Quiz>) => void;
  deleteQuiz: (id: string) => void;
  setCurrentQuiz: (quiz: Quiz | null) => void;
  startQuizAttempt: (quizId: string, userId: string) => void;
  submitAnswer: (questionId: string, answer: string) => void;
  completeQuizAttempt: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  getUserAttempts: (userId: string) => QuizAttempt[];
  getQuizById: (id: string) => Quiz | undefined;
}

type QuizStore = QuizState & QuizActions;

export const useQuizStore = create<QuizStore>((set, get) => ({
  // Initial state
  quizzes: [],
  currentQuiz: null,
  currentAttempt: null,
  userAttempts: [],
  isLoading: false,
  error: null,

  // Actions
  setQuizzes: (quizzes: Quiz[]) => {
    set({ quizzes });
  },

  addQuiz: (quiz: Quiz) => {
    set((state) => ({
      quizzes: [...state.quizzes, quiz]
    }));
  },

  updateQuiz: (id: string, updates: Partial<Quiz>) => {
    set((state) => ({
      quizzes: state.quizzes.map(quiz =>
        quiz.id === id
          ? { ...quiz, ...updates, updatedAt: new Date() }
          : quiz
      )
    }));
  },

  deleteQuiz: (id: string) => {
    set((state) => ({
      quizzes: state.quizzes.filter(quiz => quiz.id !== id)
    }));
  },

  setCurrentQuiz: (quiz: Quiz | null) => {
    set({ currentQuiz: quiz });
  },

  startQuizAttempt: (quizId: string, userId: string) => {
    const quiz = get().getQuizById(quizId);
    if (!quiz) return;

    const attempt: QuizAttempt = {
      id: `attempt_${Date.now()}`,
      quizId,
      userId,
      answers: {},
      score: 0,
      maxScore: quiz.questions.reduce((sum, q) => sum + q.points, 0),
      percentage: 0,
      timeSpent: 0,
      completedAt: new Date(),
      isCompleted: false
    };

    set({ currentAttempt: attempt });
  },

  submitAnswer: (questionId: string, answer: string) => {
    set((state) => {
      if (!state.currentAttempt) return state;

      return {
        currentAttempt: {
          ...state.currentAttempt,
          answers: {
            ...state.currentAttempt.answers,
            [questionId]: answer
          }
        }
      };
    });
  },

  completeQuizAttempt: () => {
    set((state) => {
      if (!state.currentAttempt || !state.currentQuiz) return state;

      // Calculate score
      let correctAnswers = 0;
      let totalPoints = 0;

      state.currentQuiz.questions.forEach(question => {
        const userAnswer = state.currentAttempt!.answers[question.id];
        if (userAnswer === question.correctAnswer) {
          correctAnswers += question.points;
        }
        totalPoints += question.points;
      });

      const percentage = Math.round((correctAnswers / totalPoints) * 100);

      const completedAttempt: QuizAttempt = {
        ...state.currentAttempt,
        score: correctAnswers,
        percentage,
        isCompleted: true,
        completedAt: new Date()
      };

      return {
        currentAttempt: null,
        userAttempts: [...state.userAttempts, completedAttempt]
      };
    });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  getUserAttempts: (userId: string) => {
    return get().userAttempts.filter(attempt => attempt.userId === userId);
  },

  getQuizById: (id: string) => {
    return get().quizzes.find(quiz => quiz.id === id);
  }
}));