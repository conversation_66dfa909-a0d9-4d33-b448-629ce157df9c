# QuizCraft AI - Production-Ready Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application Environment
NODE_ENV=development
VITE_APP_NAME=QuizCraft AI
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Intelligent Learning Platform
VITE_APP_ENVIRONMENT=development

# Application URLs
VITE_APP_URL=http://localhost:5173
VITE_API_BASE_URL=http://localhost:3001/api
VITE_WEBSOCKET_URL=ws://localhost:3001
VITE_WS_URL=ws://localhost:3001/ws

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Core Features
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_AI_GENERATION=true
VITE_ENABLE_MULTIPLAYER=true
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_REAL_TIME=true

# Advanced Features
VITE_ENABLE_VOICE_RECOGNITION=false
VITE_ENABLE_VIDEO_CALLS=false
VITE_ENABLE_OFFLINE_MODE=true

# Development Features
VITE_ENABLE_DEBUG_MODE=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Features
VITE_CSRF_ENABLED=true
VITE_CSP_ENABLED=true
VITE_RATE_LIMITING_ENABLED=true
VITE_INPUT_SANITIZATION_ENABLED=true

# Authentication
VITE_JWT_SECRET=your-super-secret-jwt-key-change-in-production
VITE_JWT_EXPIRES_IN=24h
VITE_REFRESH_TOKEN_EXPIRES_IN=7d

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# AI/ML Services
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here
VITE_GOOGLE_AI_API_KEY=your_google_ai_api_key_here

# Database Services
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Cloud Storage
VITE_AWS_ACCESS_KEY_ID=your_aws_access_key
VITE_AWS_SECRET_ACCESS_KEY=your_aws_secret_key
VITE_AWS_REGION=us-east-1
VITE_AWS_S3_BUCKET=quizcraft-storage

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================

# Analytics
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
VITE_MIXPANEL_TOKEN=your_mixpanel_token
VITE_HOTJAR_ID=your_hotjar_id

# Error Tracking
VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_SENTRY_ENVIRONMENT=development

# Performance Monitoring
VITE_NEW_RELIC_LICENSE_KEY=your_newrelic_key
VITE_DATADOG_API_KEY=your_datadog_key

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# API Rate Limits (requests per 15 minutes)
VITE_RATE_LIMIT_API_REQUESTS=100
VITE_RATE_LIMIT_API_WINDOW=900000

# User Action Limits (per hour)
VITE_RATE_LIMIT_QUIZ_CREATION=10
VITE_RATE_LIMIT_QUIZ_ATTEMPTS=50
VITE_RATE_LIMIT_AI_GENERATION=20

# Authentication Limits (per 15 minutes)
VITE_RATE_LIMIT_LOGIN_ATTEMPTS=5
VITE_RATE_LIMIT_REGISTRATION_ATTEMPTS=3
VITE_RATE_LIMIT_PASSWORD_RESET=3

# =============================================================================
# NOTIFICATION SERVICES
# =============================================================================

# Email Services
VITE_SMTP_HOST=smtp.gmail.com
VITE_SMTP_PORT=587
VITE_SMTP_USER=<EMAIL>
VITE_SMTP_PASS=your_app_password
VITE_FROM_EMAIL=<EMAIL>

# Push Notifications
VITE_VAPID_PUBLIC_KEY=your_vapid_public_key
VITE_VAPID_PRIVATE_KEY=your_vapid_private_key

# =============================================================================
# SOCIAL AUTHENTICATION
# =============================================================================

# Google OAuth
VITE_GOOGLE_CLIENT_ID=your_google_client_id
VITE_GOOGLE_CLIENT_SECRET=your_google_client_secret

# GitHub OAuth
VITE_GITHUB_CLIENT_ID=your_github_client_id
VITE_GITHUB_CLIENT_SECRET=your_github_client_secret

# Microsoft OAuth
VITE_MICROSOFT_CLIENT_ID=your_microsoft_client_id
VITE_MICROSOFT_CLIENT_SECRET=your_microsoft_client_secret

# =============================================================================
# PAYMENT PROCESSING
# =============================================================================

# Stripe
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key
VITE_STRIPE_SECRET_KEY=sk_test_your_stripe_secret

# PayPal
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id
VITE_PAYPAL_MODE=sandbox

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# Debugging
DEBUG=quizcraft:*
LOG_LEVEL=debug
VITE_ENABLE_QUERY_LOGGING=true

# Testing
JEST_TIMEOUT=30000
VITE_TEST_MODE=false

# =============================================================================
# PRODUCTION OVERRIDES (Uncomment for production)
# =============================================================================

# Production Security
# VITE_CSRF_ENABLED=true
# VITE_CSP_ENABLED=true
# VITE_RATE_LIMITING_ENABLED=true
# VITE_SENTRY_ENVIRONMENT=production
# LOG_LEVEL=info
# VITE_ENABLE_QUERY_LOGGING=false

# Production URLs
# VITE_APP_URL=https://app.quizcraft.ai
# VITE_API_BASE_URL=https://api.quizcraft.ai
# VITE_WEBSOCKET_URL=wss://ws.quizcraft.ai