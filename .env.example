# QuizCraft AI Environment Variables

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api
VITE_WEBSOCKET_URL=ws://localhost:3001

# External Services (Optional)
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Feature Flags
VITE_ENABLE_AI_FEATURES=true
VITE_ENABLE_MULTIPLAYER=true
VITE_ENABLE_ANALYTICS=true

# App Configuration
VITE_APP_NAME=QuizCraft AI
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# Third-party Integrations
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
VITE_SENTRY_DSN=your_sentry_dsn_here