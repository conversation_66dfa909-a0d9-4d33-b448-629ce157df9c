/**
 * Comprehensive Multiplayer Service
 * Handles real-time multiplayer quiz functionality, room management, and player interactions
 */

import { MultiplayerWebSocket } from './websocket';
import { rateLimiter } from './rate-limiter';
import { logger } from './logger';
import { apiClient } from './api';
import { GameRoom, Player, GameQuestion, GameState } from '../types';

interface CreateRoomOptions {
  name: string;
  quizId: string;
  maxPlayers: number;
  isPrivate: boolean;
  gameMode: 'speed' | 'accuracy' | 'survival';
  difficulty: 'easy' | 'medium' | 'hard';
  timePerQuestion: number;
  questionCount: number;
  allowSpectators: boolean;
}

interface JoinRoomOptions {
  roomId: string;
  password?: string;
  asSpectator?: boolean;
}

interface GameEvents {
  'room_created': (room: GameRoom) => void;
  'room_updated': (room: GameRoom) => void;
  'player_joined': (player: Player) => void;
  'player_left': (playerId: string) => void;
  'player_ready': (playerId: string) => void;
  'game_started': (gameState: GameState) => void;
  'question_started': (question: GameQuestion, timeLimit: number) => void;
  'answer_submitted': (playerId: string, answer: string, timeRemaining: number) => void;
  'question_ended': (results: Array<{ playerId: string; correct: boolean; points: number; timeUsed: number }>) => void;
  'game_ended': (finalResults: Array<{ playerId: string; totalPoints: number; rank: number }>) => void;
  'player_eliminated': (playerId: string) => void;
  'error': (error: string) => void;
  'connection_lost': () => void;
  'reconnected': () => void;
}

class MultiplayerService {
  private ws: MultiplayerWebSocket;
  private currentRoom: GameRoom | null = null;
  private currentPlayer: Player | null = null;
  private eventListeners: Map<keyof GameEvents, Function[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;

  constructor() {
    this.ws = new MultiplayerWebSocket();
    this.setupWebSocketHandlers();
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    this.ws.on('room_created', (data) => {
      this.currentRoom = data.room;
      this.emit('room_created', data.room);
      logger.trackUserAction('multiplayer_room_created', { roomId: data.room.id });
    });

    this.ws.on('room_updated', (data) => {
      this.currentRoom = data.room;
      this.emit('room_updated', data.room);
    });

    this.ws.on('player_joined', (data) => {
      if (this.currentRoom) {
        const existingPlayerIndex = this.currentRoom.players.findIndex(p => p.id === data.player.id);
        if (existingPlayerIndex >= 0) {
          this.currentRoom.players[existingPlayerIndex] = data.player;
        } else {
          this.currentRoom.players.push(data.player);
        }
      }
      this.emit('player_joined', data.player);
      logger.trackUserAction('multiplayer_player_joined', { playerId: data.player.id });
    });

    this.ws.on('player_left', (data) => {
      if (this.currentRoom) {
        this.currentRoom.players = this.currentRoom.players.filter(p => p.id !== data.playerId);
      }
      this.emit('player_left', data.playerId);
    });

    this.ws.on('player_ready', (data) => {
      if (this.currentRoom) {
        const player = this.currentRoom.players.find(p => p.id === data.playerId);
        if (player) {
          player.isReady = true;
        }
      }
      this.emit('player_ready', data.playerId);
    });

    this.ws.on('game_started', (data) => {
      this.emit('game_started', data.gameState);
      logger.trackUserAction('multiplayer_game_started', { roomId: this.currentRoom?.id });
    });

    this.ws.on('question_started', (data) => {
      this.emit('question_started', data.question, data.timeLimit);
    });

    this.ws.on('answer_submitted', (data) => {
      this.emit('answer_submitted', data.playerId, data.answer, data.timeRemaining);
    });

    this.ws.on('question_ended', (data) => {
      this.emit('question_ended', data.results);
    });

    this.ws.on('game_ended', (data) => {
      this.emit('game_ended', data.finalResults);
      logger.trackUserAction('multiplayer_game_ended', { roomId: this.currentRoom?.id });
    });

    this.ws.on('player_eliminated', (data) => {
      this.emit('player_eliminated', data.playerId);
    });

    this.ws.on('error', (data) => {
      this.emit('error', data.message);
      logger.error('Multiplayer error', 'multiplayer-service', { error: data.message });
    });

    this.ws.on('connection_lost', () => {
      this.emit('connection_lost');
      this.handleReconnection();
    });

    this.ws.on('reconnected', () => {
      this.reconnectAttempts = 0;
      this.emit('reconnected');
      
      // Rejoin current room if exists
      if (this.currentRoom && this.currentPlayer) {
        this.rejoinRoom();
      }
    });
  }

  /**
   * Connect to multiplayer service
   */
  async connect(): Promise<void> {
    try {
      await this.ws.connect();
      logger.info('Connected to multiplayer service', 'multiplayer-service');
    } catch (error) {
      logger.error('Failed to connect to multiplayer service', 'multiplayer-service', { error });
      throw error;
    }
  }

  /**
   * Disconnect from multiplayer service
   */
  disconnect(): void {
    this.ws.disconnect();
    this.currentRoom = null;
    this.currentPlayer = null;
    logger.info('Disconnected from multiplayer service', 'multiplayer-service');
  }

  /**
   * Create a new game room
   */
  async createRoom(options: CreateRoomOptions, userId: string): Promise<GameRoom> {
    // Check rate limiting
    const rateLimitResult = rateLimiter.checkRoomCreationLimit(userId);
    if (!rateLimitResult.allowed) {
      throw new Error(rateLimitResult.message || 'Room creation rate limit exceeded');
    }

    try {
      logger.info('Creating multiplayer room', 'multiplayer-service', { userId, options });

      // First create room via API
      const response = await apiClient.request('/multiplayer/rooms', {
        method: 'POST',
        body: {
          ...options,
          hostId: userId,
          createdAt: new Date()
        }
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to create room');
      }

      const room = response.data;

      // Then join via WebSocket
      this.ws.send('create_room', {
        room,
        hostId: userId
      });

      return room;
    } catch (error) {
      logger.error('Room creation failed', 'multiplayer-service', { userId, error });
      throw error;
    }
  }

  /**
   * Join an existing game room
   */
  async joinRoom(options: JoinRoomOptions, player: Player): Promise<void> {
    try {
      logger.info('Joining multiplayer room', 'multiplayer-service', { 
        playerId: player.id, 
        roomId: options.roomId 
      });

      // First validate room via API
      const response = await apiClient.request(`/multiplayer/rooms/${options.roomId}`);

      if (!response.success) {
        throw new Error(response.error || 'Room not found');
      }

      const room = response.data;

      // Check if room is full
      if (!options.asSpectator && room.players.length >= room.maxPlayers) {
        throw new Error('Room is full');
      }

      // Check if game is in progress
      if (room.status === 'active' && !options.asSpectator) {
        throw new Error('Game is already in progress');
      }

      // Join via WebSocket
      this.ws.send('join_room', {
        roomId: options.roomId,
        player,
        password: options.password,
        asSpectator: options.asSpectator
      });

      this.currentPlayer = player;
    } catch (error) {
      logger.error('Room join failed', 'multiplayer-service', { 
        playerId: player.id, 
        roomId: options.roomId, 
        error 
      });
      throw error;
    }
  }

  /**
   * Leave current room
   */
  leaveRoom(): void {
    if (this.currentRoom && this.currentPlayer) {
      this.ws.send('leave_room', {
        roomId: this.currentRoom.id,
        playerId: this.currentPlayer.id
      });

      logger.trackUserAction('multiplayer_leave_room', { 
        roomId: this.currentRoom.id,
        playerId: this.currentPlayer.id
      });

      this.currentRoom = null;
      this.currentPlayer = null;
    }
  }

  /**
   * Mark player as ready
   */
  setReady(ready: boolean): void {
    if (this.currentRoom && this.currentPlayer) {
      this.ws.send('player_ready', {
        roomId: this.currentRoom.id,
        playerId: this.currentPlayer.id,
        ready
      });

      this.currentPlayer.isReady = ready;
    }
  }

  /**
   * Start the game (host only)
   */
  startGame(): void {
    if (this.currentRoom && this.currentPlayer?.isHost) {
      this.ws.send('start_game', {
        roomId: this.currentRoom.id
      });

      logger.trackUserAction('multiplayer_start_game', { roomId: this.currentRoom.id });
    }
  }

  /**
   * Submit answer to current question
   */
  submitAnswer(answer: string): void {
    // Check rate limiting for game actions
    if (this.currentPlayer) {
      const rateLimitResult = rateLimiter.checkGameActionsLimit(this.currentPlayer.id);
      if (!rateLimitResult.allowed) {
        this.emit('error', rateLimitResult.message || 'Too many actions');
        return;
      }
    }

    if (this.currentRoom && this.currentPlayer) {
      this.ws.send('submit_answer', {
        roomId: this.currentRoom.id,
        playerId: this.currentPlayer.id,
        answer,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Send chat message
   */
  sendChatMessage(message: string): void {
    if (this.currentRoom && this.currentPlayer) {
      this.ws.send('chat_message', {
        roomId: this.currentRoom.id,
        playerId: this.currentPlayer.id,
        message,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Get available rooms
   */
  async getAvailableRooms(filters?: {
    gameMode?: string;
    difficulty?: string;
    hasSpace?: boolean;
  }): Promise<GameRoom[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.gameMode) params.append('gameMode', filters.gameMode);
      if (filters?.difficulty) params.append('difficulty', filters.difficulty);
      if (filters?.hasSpace) params.append('hasSpace', 'true');

      const response = await apiClient.request(`/multiplayer/rooms?${params}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get rooms');
      }

      return response.data;
    } catch (error) {
      logger.error('Failed to get available rooms', 'multiplayer-service', { error });
      throw error;
    }
  }

  /**
   * Handle reconnection logic
   */
  private async handleReconnection(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('error', 'Maximum reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    
    setTimeout(async () => {
      try {
        await this.ws.connect();
      } catch (error) {
        logger.error('Reconnection failed', 'multiplayer-service', { 
          attempt: this.reconnectAttempts, 
          error 
        });
        this.handleReconnection();
      }
    }, this.reconnectDelay * this.reconnectAttempts);
  }

  /**
   * Rejoin current room after reconnection
   */
  private rejoinRoom(): void {
    if (this.currentRoom && this.currentPlayer) {
      this.ws.send('rejoin_room', {
        roomId: this.currentRoom.id,
        playerId: this.currentPlayer.id
      });
    }
  }

  /**
   * Add event listener
   */
  on<K extends keyof GameEvents>(event: K, listener: GameEvents[K]): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(listener);
  }

  /**
   * Remove event listener
   */
  off<K extends keyof GameEvents>(event: K, listener: GameEvents[K]): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  private emit<K extends keyof GameEvents>(event: K, ...args: Parameters<GameEvents[K]>): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          (listener as any)(...args);
        } catch (error) {
          logger.error('Event listener error', 'multiplayer-service', { event, error });
        }
      });
    }
  }

  /**
   * Get current room
   */
  getCurrentRoom(): GameRoom | null {
    return this.currentRoom;
  }

  /**
   * Get current player
   */
  getCurrentPlayer(): Player | null {
    return this.currentPlayer;
  }

  /**
   * Check if connected
   */
  isConnected(): boolean {
    return this.ws.isConnected();
  }
}

// Create singleton instance
export const multiplayerService = new MultiplayerService();

// Export types
export type { CreateRoomOptions, JoinRoomOptions, GameEvents };
