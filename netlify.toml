# Netlify Configuration for QuizCraft AI
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

# Redirect all routes to index.html for SPA routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self' https:;"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache fonts
[[headers]]
  for = "*.woff2"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Environment variables (set in Netlify dashboard)
# VITE_API_BASE_URL
# VITE_WEBSOCKET_URL
# VITE_OPENAI_API_KEY (if using OpenAI)
# VITE_SUPABASE_URL (if using Supabase)
# VITE_SUPABASE_ANON_KEY (if using Supabase)