import React from 'react';
import { 
  <PERSON>, 
  Users, 
  BarChart3, 
  Trophy, 
  Clock, 
  TrendingUp, 
  Target,
  Zap,
  BookOpen,
  Star,
  ChevronRight,
  Play,
  Plus
} from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { sampleQuizzes } from '../data/sampleData';

interface DashboardProps {
  onNavigate: (page: string) => void;
}

const Dashboard: React.FC<DashboardProps> = ({ onNavigate }) => {
  const { user } = useAuthStore();

  // Transform sample quizzes into recent quiz format
  const recentQuizzes = sampleQuizzes.slice(0, 3).map((quiz, index) => ({
    id: quiz.id,
    title: quiz.title,
    category: quiz.category,
    score: Math.floor(Math.random() * 20) + 75, // Random score between 75-95
    questionsCount: quiz.questions.length,
    completedAt: index === 0 ? '2 hours ago' : index === 1 ? '1 day ago' : '3 days ago',
    difficulty: quiz.difficulty === 'easy' ? 'Beginner' : quiz.difficulty === 'medium' ? 'Intermediate' : 'Advanced',
    color: index === 0 ? 'bg-blue-500' : index === 1 ? 'bg-green-500' : 'bg-purple-500'
  }));

  // Transform remaining quizzes into recommendations
  const recommendations = sampleQuizzes.slice(3, 6).map((quiz, index) => ({
    id: quiz.id,
    title: quiz.title,
    description: quiz.description.substring(0, 50) + '...',
    estimatedTime: `${Math.floor(quiz.questions.length * 1.5)} min`,
    difficulty: quiz.difficulty === 'easy' ? 'Beginner' : quiz.difficulty === 'medium' ? 'Intermediate' : 'Advanced',
    matchScore: Math.floor(Math.random() * 15) + 85 // Random score between 85-100
  }));

  const achievements = [
    { icon: Trophy, title: 'Quiz Master', description: 'Complete 50 quizzes', progress: 94 },
    { icon: Zap, title: 'Speed Demon', description: 'Answer 100 questions in under 30s each', progress: 67 },
    { icon: Target, title: 'Perfectionist', description: 'Score 100% on 10 quizzes', progress: 40 },
    { icon: Star, title: 'Knowledge Seeker', description: 'Explore 10 different categories', progress: 80 }
  ];

  const stats = [
    {
      label: 'Total Quizzes',
      value: user?.stats.totalQuizzes || 0,
      change: '+12%',
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      label: 'Average Score',
      value: `${user?.stats.averageScore || 0}%`,
      change: '+5%',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      label: 'Current Streak',
      value: `${user?.stats.streak || 0} days`,
      change: '+2',
      icon: Clock,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      label: 'Global Rank',
      value: `#${user?.stats.rank || 0}`,
      change: '+23',
      icon: Trophy,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ];

  return (
    <div className="p-4 md:p-6 lg:p-8 max-w-7xl mx-auto">
      {/* Welcome Header */}
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user?.name?.split(' ')[0]}! 👋
        </h1>
        <p className="text-gray-600">
          Ready to expand your knowledge? Let's continue your learning journey.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <button
          onClick={() => onNavigate('create')}
          className="bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
        >
          <div className="flex items-center justify-between">
            <div className="text-left">
              <h3 className="text-lg font-semibold mb-1">Create Quiz</h3>
              <p className="text-indigo-100 text-sm">Generate AI-powered quizzes</p>
            </div>
            <Plus className="w-8 h-8 group-hover:scale-110 transition-transform" />
          </div>
        </button>

        <button
          onClick={() => onNavigate('take')}
          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
        >
          <div className="flex items-center justify-between">
            <div className="text-left">
              <h3 className="text-lg font-semibold mb-1">Take Quiz</h3>
              <p className="text-green-100 text-sm">Challenge yourself</p>
            </div>
            <Play className="w-8 h-8 group-hover:scale-110 transition-transform" />
          </div>
        </button>

        <button
          onClick={() => onNavigate('multiplayer')}
          className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
        >
          <div className="flex items-center justify-between">
            <div className="text-left">
              <h3 className="text-lg font-semibold mb-1">Battle Mode</h3>
              <p className="text-orange-100 text-sm">Compete with others</p>
            </div>
            <Users className="w-8 h-8 group-hover:scale-110 transition-transform" />
          </div>
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white p-4 md:p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`w-5 h-5 ${stat.color}`} />
                </div>
                <span className="text-xs font-medium text-green-600 bg-green-100 px-2 py-1 rounded-full">
                  {stat.change}
                </span>
              </div>
              <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
              <p className="text-sm text-gray-600">{stat.label}</p>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Recent Quizzes */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Recent Quizzes</h2>
              <button
                onClick={() => onNavigate('analytics')}
                className="text-indigo-600 hover:text-indigo-700 text-sm font-medium flex items-center"
              >
                View All <ChevronRight className="w-4 h-4 ml-1" />
              </button>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {recentQuizzes.map((quiz) => (
                <div key={quiz.id} className="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors group cursor-pointer">
                  <div className={`w-12 h-12 ${quiz.color} rounded-lg flex items-center justify-center mr-4`}>
                    <Brain className="w-6 h-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 group-hover:text-indigo-600 transition-colors">
                      {quiz.title}
                    </h3>
                    <div className="flex items-center space-x-4 mt-1">
                      <span className="text-sm text-gray-500">{quiz.category}</span>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-500">{quiz.questionsCount} questions</span>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-500">{quiz.completedAt}</span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-semibold text-gray-900">{quiz.score}%</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      quiz.difficulty === 'Advanced' ? 'bg-red-100 text-red-700' :
                      quiz.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {quiz.difficulty}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Achievements */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-xl font-semibold text-gray-900">Achievements</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {achievements.map((achievement, index) => {
                const Icon = achievement.icon;
                return (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <Icon className="w-5 h-5 text-yellow-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900">{achievement.title}</h4>
                      <p className="text-xs text-gray-600 mt-1">{achievement.description}</p>
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">Progress</span>
                          <span className="font-medium text-gray-900">{achievement.progress}%</span>
                        </div>
                        <div className="mt-1 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${achievement.progress}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* AI Recommendations */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-indigo-600" />
            <h2 className="text-xl font-semibold text-gray-900">AI Recommendations</h2>
          </div>
          <p className="text-gray-600 text-sm mt-1">Personalized quizzes based on your learning patterns</p>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {recommendations.map((rec) => (
              <div key={rec.id} className="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 hover:shadow-md transition-all duration-200 cursor-pointer group">
                <div className="flex items-center justify-between mb-3">
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                    rec.difficulty === 'Advanced' ? 'bg-red-100 text-red-700' :
                    rec.difficulty === 'Intermediate' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-green-100 text-green-700'
                  }`}>
                    {rec.difficulty}
                  </span>
                  <span className="text-xs font-medium text-indigo-600 bg-indigo-100 px-2 py-1 rounded-full">
                    {rec.matchScore}% match
                  </span>
                </div>
                <h3 className="font-medium text-gray-900 group-hover:text-indigo-600 transition-colors mb-2">
                  {rec.title}
                </h3>
                <p className="text-sm text-gray-600 mb-3">{rec.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-1 text-xs text-gray-500">
                    <Clock className="w-3 h-3" />
                    <span>{rec.estimatedTime}</span>
                  </div>
                  <button
                    onClick={() => onNavigate('take')}
                    className="text-xs font-medium text-indigo-600 hover:text-indigo-700 flex items-center"
                  >
                    Start <ChevronRight className="w-3 h-3 ml-1" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;