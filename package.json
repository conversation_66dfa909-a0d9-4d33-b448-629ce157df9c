{"name": "quizcraft-ai", "private": true, "version": "1.0.0", "description": "QuizCraft AI - Intelligent Learning Platform with AI-powered quiz generation, real-time multiplayer, and comprehensive analytics", "type": "module", "author": "QuizCraft AI Team <<EMAIL>>", "license": "MIT", "homepage": "https://quizcraft.ai", "repository": {"type": "git", "url": "https://github.com/quizcraft-ai/quizcraft-frontend.git"}, "bugs": {"url": "https://github.com/quizcraft-ai/quizcraft-frontend/issues"}, "keywords": ["quiz", "ai", "education", "learning", "react", "typescript", "vite", "multiplayer", "analytics"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc && vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "build:production": "NODE_ENV=production npm run build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview --host 0.0.0.0", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --coverage --reporter=junit --outputFile=test-results.xml", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "docker:build": "docker build -t quizcraft-ai .", "docker:run": "docker run -p 80:80 quizcraft-ai", "docker:dev": "docker build --target development -t quizcraft-ai-dev . && docker run -p 5173:5173 -v $(pwd):/app quizcraft-ai-dev", "docker:compose": "docker-compose up -d", "docker:compose:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up", "docker:compose:down": "docker-compose down", "deploy:staging": "npm run build:production && npm run docker:build && docker tag quizcraft-ai quizcraft-ai:staging", "deploy:production": "npm run build:production && npm run docker:build && docker tag quizcraft-ai quizcraft-ai:latest", "health-check": "curl -f http://localhost/health || exit 1", "start": "npm run preview", "clean": "rm -rf dist node_modules/.vite", "reinstall": "rm -rf node_modules package-lock.json && npm install", "security:audit": "npm audit --audit-level moderate", "security:fix": "npm audit fix"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.0", "uuid": "^11.1.0", "zod": "^4.0.5", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}