{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.20", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.0", "uuid": "^11.1.0", "zod": "^4.0.5", "zustand": "^4.4.7"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}