/**
 * Comprehensive Validation Utilities
 * Provides validation functions for forms, inputs, and data structures
 */

import { QUIZ_CONSTANTS, ERROR_MESSAGES } from './constants';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// Field validation result
export interface FieldValidationResult {
  isValid: boolean;
  error?: string;
  warning?: string;
}

/**
 * Email validation
 */
export const validateEmail = (email: string): FieldValidationResult => {
  if (!email) {
    return { isValid: false, error: 'Email is required' };
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  if (email.length > 254) {
    return { isValid: false, error: 'Email address is too long' };
  }

  return { isValid: true };
};

/**
 * Password validation
 */
export const validatePassword = (password: string): FieldValidationResult => {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }

  if (password.length < 8) {
    return { isValid: false, error: 'Password must be at least 8 characters long' };
  }

  if (password.length > 128) {
    return { isValid: false, error: 'Password is too long (max 128 characters)' };
  }

  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecialChar = /[@$!%*?&]/.test(password);

  if (!hasLowercase) {
    return { isValid: false, error: 'Password must contain at least one lowercase letter' };
  }

  if (!hasUppercase) {
    return { isValid: false, error: 'Password must contain at least one uppercase letter' };
  }

  if (!hasNumber) {
    return { isValid: false, error: 'Password must contain at least one number' };
  }

  if (!hasSpecialChar) {
    return { isValid: false, error: 'Password must contain at least one special character (@$!%*?&)' };
  }

  // Check for common weak passwords
  const commonPasswords = ['password', '12345678', 'qwerty123', 'admin123'];
  if (commonPasswords.includes(password.toLowerCase())) {
    return { isValid: false, error: 'This password is too common. Please choose a stronger password' };
  }

  return { isValid: true };
};

/**
 * Name validation
 */
export const validateName = (name: string): FieldValidationResult => {
  if (!name) {
    return { isValid: false, error: 'Name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: 'Name must be at least 2 characters long' };
  }

  if (name.length > 50) {
    return { isValid: false, error: 'Name is too long (max 50 characters)' };
  }

  const nameRegex = /^[a-zA-Z\s\-'\.]+$/;
  if (!nameRegex.test(name)) {
    return { isValid: false, error: 'Name can only contain letters, spaces, hyphens, apostrophes, and periods' };
  }

  return { isValid: true };
};

/**
 * Quiz title validation
 */
export const validateQuizTitle = (title: string): FieldValidationResult => {
  if (!title) {
    return { isValid: false, error: 'Quiz title is required' };
  }

  if (title.trim().length < 3) {
    return { isValid: false, error: 'Quiz title must be at least 3 characters long' };
  }

  if (title.length > QUIZ_CONSTANTS.MAX_QUIZ_TITLE_LENGTH) {
    return { 
      isValid: false, 
      error: `Quiz title is too long (max ${QUIZ_CONSTANTS.MAX_QUIZ_TITLE_LENGTH} characters)` 
    };
  }

  // Check for inappropriate content (basic check)
  const inappropriateWords = ['spam', 'test123', 'untitled'];
  if (inappropriateWords.some(word => title.toLowerCase().includes(word))) {
    return { 
      isValid: true, 
      warning: 'Consider using a more descriptive title' 
    };
  }

  return { isValid: true };
};

/**
 * Quiz description validation
 */
export const validateQuizDescription = (description: string): FieldValidationResult => {
  if (description && description.length > QUIZ_CONSTANTS.MAX_QUIZ_DESCRIPTION_LENGTH) {
    return { 
      isValid: false, 
      error: `Description is too long (max ${QUIZ_CONSTANTS.MAX_QUIZ_DESCRIPTION_LENGTH} characters)` 
    };
  }

  return { isValid: true };
};

/**
 * Question validation
 */
export const validateQuestion = (question: string): FieldValidationResult => {
  if (!question) {
    return { isValid: false, error: 'Question text is required' };
  }

  if (question.trim().length < 10) {
    return { isValid: false, error: 'Question must be at least 10 characters long' };
  }

  if (question.length > QUIZ_CONSTANTS.MAX_QUESTION_LENGTH) {
    return { 
      isValid: false, 
      error: `Question is too long (max ${QUIZ_CONSTANTS.MAX_QUESTION_LENGTH} characters)` 
    };
  }

  // Check if question ends with question mark
  if (!question.trim().endsWith('?')) {
    return { 
      isValid: true, 
      warning: 'Consider ending your question with a question mark' 
    };
  }

  return { isValid: true };
};

/**
 * Multiple choice options validation
 */
export const validateMultipleChoiceOptions = (options: string[]): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (options.length < 2) {
    errors.push('At least 2 options are required');
  }

  if (options.length > 6) {
    errors.push('Maximum 6 options allowed');
  }

  options.forEach((option, index) => {
    if (!option || option.trim().length === 0) {
      errors.push(`Option ${index + 1} cannot be empty`);
    } else if (option.length > QUIZ_CONSTANTS.MAX_OPTION_LENGTH) {
      errors.push(`Option ${index + 1} is too long (max ${QUIZ_CONSTANTS.MAX_OPTION_LENGTH} characters)`);
    }
  });

  // Check for duplicate options
  const uniqueOptions = new Set(options.map(opt => opt.toLowerCase().trim()));
  if (uniqueOptions.size !== options.length) {
    errors.push('Options must be unique');
  }

  // Check for very similar options
  for (let i = 0; i < options.length; i++) {
    for (let j = i + 1; j < options.length; j++) {
      const similarity = calculateSimilarity(options[i], options[j]);
      if (similarity > 0.8) {
        warnings.push(`Options ${i + 1} and ${j + 1} are very similar`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Quiz settings validation
 */
export const validateQuizSettings = (settings: {
  timeLimit: number;
  questionCount: number;
  difficulty: string;
  category: string;
}): ValidationResult => {
  const errors: string[] = [];

  if (settings.timeLimit < QUIZ_CONSTANTS.MIN_TIME_LIMIT) {
    errors.push(`Time limit must be at least ${QUIZ_CONSTANTS.MIN_TIME_LIMIT} minutes`);
  }

  if (settings.timeLimit > QUIZ_CONSTANTS.MAX_TIME_LIMIT) {
    errors.push(`Time limit cannot exceed ${QUIZ_CONSTANTS.MAX_TIME_LIMIT} minutes`);
  }

  if (settings.questionCount < QUIZ_CONSTANTS.MIN_QUESTIONS) {
    errors.push(`At least ${QUIZ_CONSTANTS.MIN_QUESTIONS} question is required`);
  }

  if (settings.questionCount > QUIZ_CONSTANTS.MAX_QUESTIONS) {
    errors.push(`Maximum ${QUIZ_CONSTANTS.MAX_QUESTIONS} questions allowed`);
  }

  if (!settings.difficulty) {
    errors.push('Difficulty level is required');
  }

  if (!settings.category) {
    errors.push('Category is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * File validation
 */
export const validateFile = (file: File, allowedTypes: string[], maxSize: number): FieldValidationResult => {
  if (!file) {
    return { isValid: false, error: 'File is required' };
  }

  if (!allowedTypes.includes(file.type)) {
    return { 
      isValid: false, 
      error: `File type not supported. Allowed types: ${allowedTypes.join(', ')}` 
    };
  }

  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    return { 
      isValid: false, 
      error: `File size too large. Maximum size: ${maxSizeMB}MB` 
    };
  }

  return { isValid: true };
};

/**
 * URL validation
 */
export const validateUrl = (url: string): FieldValidationResult => {
  if (!url) {
    return { isValid: false, error: 'URL is required' };
  }

  try {
    const urlObj = new URL(url);
    
    // Check protocol
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: 'URL must use HTTP or HTTPS protocol' };
    }

    // Check for localhost in production
    if (process.env.NODE_ENV === 'production' && 
        (urlObj.hostname === 'localhost' || urlObj.hostname === '127.0.0.1')) {
      return { isValid: false, error: 'Localhost URLs are not allowed' };
    }

    return { isValid: true };
  } catch {
    return { isValid: false, error: 'Please enter a valid URL' };
  }
};

/**
 * Phone number validation
 */
export const validatePhoneNumber = (phone: string): FieldValidationResult => {
  if (!phone) {
    return { isValid: false, error: 'Phone number is required' };
  }

  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');

  if (cleaned.length < 10) {
    return { isValid: false, error: 'Phone number must be at least 10 digits' };
  }

  if (cleaned.length > 15) {
    return { isValid: false, error: 'Phone number is too long' };
  }

  return { isValid: true };
};

/**
 * Calculate similarity between two strings (0-1)
 */
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  const editDistance = levenshteinDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Sanitize HTML input
 */
export const sanitizeHtml = (html: string): string => {
  const allowedTags = ['b', 'i', 'u', 'strong', 'em', 'br', 'p'];
  const allowedAttributes: Record<string, string[]> = {};

  // Simple HTML sanitization (in production, use a library like DOMPurify)
  let sanitized = html;
  
  // Remove script tags
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
  
  // Remove on* attributes
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
  
  // Remove javascript: URLs
  sanitized = sanitized.replace(/javascript:/gi, '');
  
  return sanitized;
};

/**
 * Validate and sanitize user input
 */
export const validateAndSanitizeInput = (
  input: string, 
  maxLength: number = 1000,
  allowHtml: boolean = false
): FieldValidationResult => {
  if (!input) {
    return { isValid: false, error: 'Input is required' };
  }

  if (input.length > maxLength) {
    return { isValid: false, error: `Input is too long (max ${maxLength} characters)` };
  }

  // Check for potential XSS
  const xssPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i,
    /<object/i,
    /<embed/i
  ];

  if (xssPatterns.some(pattern => pattern.test(input))) {
    return { isValid: false, error: 'Input contains potentially unsafe content' };
  }

  return { isValid: true };
};
