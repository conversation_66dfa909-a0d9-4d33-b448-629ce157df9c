#!/bin/bash

# QuizCraft AI - Comprehensive Health Check Script
# This script performs various health checks for the application

set -e

# Configuration
HEALTH_ENDPOINT="http://localhost/health"
API_ENDPOINT="http://localhost/api/health"
TIMEOUT=10
MAX_RETRIES=3
LOG_FILE="/var/log/health-check.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Success function
success() {
    echo -e "${GREEN}✓ $1${NC}"
    log "SUCCESS: $1"
}

# Warning function
warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    log "WARNING: $1"
}

# Error function
error() {
    echo -e "${RED}✗ $1${NC}"
    log "ERROR: $1"
}

# Check if a URL is accessible
check_url() {
    local url=$1
    local name=$2
    local expected_status=${3:-200}
    
    log "Checking $name at $url"
    
    for i in $(seq 1 $MAX_RETRIES); do
        if response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$url" 2>/dev/null); then
            if [ "$response" = "$expected_status" ]; then
                success "$name is healthy (HTTP $response)"
                return 0
            else
                warning "$name returned HTTP $response (expected $expected_status)"
            fi
        else
            warning "$name check failed (attempt $i/$MAX_RETRIES)"
        fi
        
        if [ $i -lt $MAX_RETRIES ]; then
            sleep 2
        fi
    done
    
    error "$name is not accessible after $MAX_RETRIES attempts"
    return 1
}

# Check disk space
check_disk_space() {
    log "Checking disk space"
    
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -lt 80 ]; then
        success "Disk usage is healthy ($usage%)"
    elif [ "$usage" -lt 90 ]; then
        warning "Disk usage is high ($usage%)"
    else
        error "Disk usage is critical ($usage%)"
        return 1
    fi
}

# Check memory usage
check_memory() {
    log "Checking memory usage"
    
    if command -v free >/dev/null 2>&1; then
        local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        
        if [ "$mem_usage" -lt 80 ]; then
            success "Memory usage is healthy ($mem_usage%)"
        elif [ "$mem_usage" -lt 90 ]; then
            warning "Memory usage is high ($mem_usage%)"
        else
            error "Memory usage is critical ($mem_usage%)"
            return 1
        fi
    else
        warning "Cannot check memory usage (free command not available)"
    fi
}

# Check if required processes are running
check_processes() {
    log "Checking required processes"
    
    local processes=("nginx")
    
    for process in "${processes[@]}"; do
        if pgrep "$process" > /dev/null; then
            success "$process is running"
        else
            error "$process is not running"
            return 1
        fi
    done
}

# Check file permissions
check_permissions() {
    log "Checking file permissions"
    
    local files=(
        "/usr/share/nginx/html:755"
        "/var/log/nginx:755"
        "/etc/nginx/nginx.conf:644"
    )
    
    for file_perm in "${files[@]}"; do
        local file="${file_perm%:*}"
        local expected_perm="${file_perm#*:}"
        
        if [ -e "$file" ]; then
            local actual_perm=$(stat -c "%a" "$file" 2>/dev/null || echo "unknown")
            if [ "$actual_perm" = "$expected_perm" ]; then
                success "$file has correct permissions ($actual_perm)"
            else
                warning "$file has incorrect permissions ($actual_perm, expected $expected_perm)"
            fi
        else
            warning "$file does not exist"
        fi
    done
}

# Check SSL certificate (if HTTPS is enabled)
check_ssl() {
    local ssl_cert="/etc/ssl/certs/quizcraft.ai.crt"
    
    if [ -f "$ssl_cert" ]; then
        log "Checking SSL certificate"
        
        local expiry_date=$(openssl x509 -in "$ssl_cert" -noout -enddate 2>/dev/null | cut -d= -f2)
        local expiry_epoch=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
        local current_epoch=$(date +%s)
        local days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
        
        if [ "$days_until_expiry" -gt 30 ]; then
            success "SSL certificate is valid ($days_until_expiry days until expiry)"
        elif [ "$days_until_expiry" -gt 7 ]; then
            warning "SSL certificate expires soon ($days_until_expiry days)"
        else
            error "SSL certificate expires very soon ($days_until_expiry days)"
            return 1
        fi
    else
        log "SSL certificate not found (HTTP mode)"
    fi
}

# Check application-specific health
check_application() {
    log "Checking application health"
    
    # Check if main HTML file exists
    if [ -f "/usr/share/nginx/html/index.html" ]; then
        success "Main application file exists"
    else
        error "Main application file is missing"
        return 1
    fi
    
    # Check if assets directory exists
    if [ -d "/usr/share/nginx/html/assets" ]; then
        success "Assets directory exists"
    else
        warning "Assets directory is missing"
    fi
}

# Main health check function
main() {
    log "Starting comprehensive health check"
    
    local exit_code=0
    
    # Perform all health checks
    check_url "$HEALTH_ENDPOINT" "Health endpoint" || exit_code=1
    check_disk_space || exit_code=1
    check_memory || exit_code=1
    check_processes || exit_code=1
    check_permissions
    check_ssl
    check_application || exit_code=1
    
    # Summary
    if [ $exit_code -eq 0 ]; then
        success "All health checks passed"
        log "Health check completed successfully"
    else
        error "Some health checks failed"
        log "Health check completed with errors"
    fi
    
    exit $exit_code
}

# Run main function
main "$@"
