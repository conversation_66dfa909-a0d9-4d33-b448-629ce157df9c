// User Types
export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
  role: 'student' | 'educator' | 'admin';
  stats: UserStats;
  preferences: UserPreferences;
  createdAt: Date;
  lastActive: Date;
}

export interface UserStats {
  totalQuizzes: number;
  averageScore: number;
  streak: number;
  rank: number;
  totalTime: number;
  bestScore: number;
  worstScore: number;
  categoriesExplored: number;
}

export interface UserPreferences {
  language: string;
  notifications: boolean;
  theme: 'light' | 'dark' | 'auto';
  sounds: boolean;
  autoPlay: boolean;
  difficulty: 'auto' | 'beginner' | 'intermediate' | 'advanced';
}

// Quiz Types
export interface Quiz {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: QuizDifficulty;
  questions: Question[];
  timeLimit: number;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  stats: QuizStats;
  tags: string[];
  settings: QuizSettings;
}

export interface QuizSettings {
  randomizeQuestions: boolean;
  randomizeOptions: boolean;
  showResults: boolean;
  allowRetakes: boolean;
  passScore: number;
  certificateEnabled: boolean;
}

export interface QuizStats {
  totalAttempts: number;
  averageScore: number;
  averageTime: number;
  passRate: number;
  popularityScore: number;
}

export type QuizDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'adaptive';

// Question Types
export interface Question {
  id: string;
  type: QuestionType;
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation: string;
  points: number;
  timeLimit?: number;
  difficulty: 'easy' | 'medium' | 'hard';
  tags: string[];
  hints?: string[];
  resources?: QuestionResource[];
}

export type QuestionType = 'multiple-choice' | 'true-false' | 'short-answer' | 'essay' | 'fill-blank' | 'matching';

export interface QuestionResource {
  title: string;
  url: string;
  type: 'article' | 'video' | 'documentation' | 'tutorial';
}

// Quiz Attempt Types
export interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  answers: Record<string, QuestionAnswer>;
  score: number;
  maxScore: number;
  percentage: number;
  timeSpent: number;
  completedAt: Date;
  isCompleted: boolean;
  isPassed: boolean;
  feedback?: AttemptFeedback;
  metadata: AttemptMetadata;
}

export interface QuestionAnswer {
  answer: string | string[];
  isCorrect: boolean;
  timeSpent: number;
  attempts: number;
  hintsUsed: number;
}

export interface AttemptFeedback {
  overallComments: string;
  strengths: string[];
  improvements: string[];
  recommendations: string[];
  nextSteps: string[];
}

export interface AttemptMetadata {
  device: string;
  browser: string;
  ip?: string;
  location?: string;
  sessionId: string;
}

// Multiplayer Types
export interface GameRoom {
  id: string;
  name: string;
  hostId: string;
  players: Player[];
  maxPlayers: number;
  quizId: string;
  quizTitle: string;
  status: GameStatus;
  gameMode: GameMode;
  difficulty: 'easy' | 'medium' | 'hard';
  settings: GameSettings;
  currentQuestion?: number;
  createdAt: Date;
  startedAt?: Date;
  endedAt?: Date;
}

export interface Player {
  id: string;
  name: string;
  avatar: string;
  score: number;
  isReady: boolean;
  isHost: boolean;
  isConnected: boolean;
  joinedAt: Date;
  rank?: number;
  answers?: Record<string, PlayerAnswer>;
}

export interface PlayerAnswer {
  questionId: string;
  answer: string;
  timeSpent: number;
  isCorrect: boolean;
  submittedAt: Date;
  points: number;
}

export type GameStatus = 'waiting' | 'starting' | 'active' | 'paused' | 'finished';
export type GameMode = 'speed' | 'accuracy' | 'survival' | 'elimination';

export interface GameSettings {
  timePerQuestion: number;
  questionCount: number;
  isPrivate: boolean;
  allowSpectators: boolean;
  enableChat: boolean;
  showLiveResults: boolean;
  eliminationThreshold?: number;
}

// Analytics Types
export interface AnalyticsData {
  user: UserAnalytics;
  performance: PerformanceMetrics;
  learning: LearningMetrics;
  engagement: EngagementMetrics;
}

export interface UserAnalytics {
  totalQuizzes: number;
  averageScore: number;
  totalTime: number;
  streak: number;
  rank: number;
  improvement: number;
  consistency: number;
}

export interface PerformanceMetrics {
  accuracyTrend: number[];
  speedTrend: number[];
  difficultyProgression: DifficultyMetric[];
  categoryPerformance: CategoryMetric[];
  timeDistribution: TimeMetric[];
}

export interface LearningMetrics {
  knowledgeGaps: KnowledgeGap[];
  masteredTopics: string[];
  learningPath: LearningStep[];
  retentionRate: number;
  progressRate: number;
}

export interface EngagementMetrics {
  sessionDuration: number;
  sessionsPerWeek: number;
  featureUsage: FeatureUsage[];
  socialInteractions: number;
  achievementsUnlocked: number;
}

export interface DifficultyMetric {
  difficulty: string;
  accuracy: number;
  count: number;
  improvement: number;
}

export interface CategoryMetric {
  category: string;
  score: number;
  count: number;
  improvement: number;
  timeSpent: number;
}

export interface TimeMetric {
  hour: number;
  accuracy: number;
  speed: number;
  engagement: number;
}

export interface KnowledgeGap {
  topic: string;
  accuracy: number;
  confidence: number;
  priority: 'high' | 'medium' | 'low';
  recommendation: string;
  estimatedTime: number;
}

export interface LearningStep {
  topic: string;
  status: 'completed' | 'current' | 'upcoming';
  progress: number;
  estimatedTime: number;
  prerequisites: string[];
}

export interface FeatureUsage {
  feature: string;
  usage: number;
  trend: 'increasing' | 'stable' | 'decreasing';
}

// Achievement Types
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: AchievementCategory;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  requirements: AchievementRequirement[];
  reward?: AchievementReward;
  unlockedAt?: Date;
  progress: number;
}

export type AchievementCategory = 'learning' | 'social' | 'performance' | 'consistency' | 'exploration';

export interface AchievementRequirement {
  type: 'quiz_count' | 'score_average' | 'streak' | 'category_mastery' | 'time_spent' | 'social_interaction';
  target: number;
  current: number;
}

export interface AchievementReward {
  type: 'badge' | 'title' | 'avatar' | 'theme' | 'feature';
  value: string;
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: Date;
  expiresAt?: Date;
  actionUrl?: string;
}

export type NotificationType = 
  | 'quiz_completed' 
  | 'achievement_unlocked' 
  | 'streak_milestone' 
  | 'multiplayer_invite' 
  | 'new_quiz_available' 
  | 'system_update'
  | 'reminder';

// Content Types
export interface ContentSource {
  id: string;
  type: 'text' | 'url' | 'file' | 'image' | 'audio' | 'video';
  content: string;
  metadata: ContentMetadata;
  extractedContent?: ExtractedContent;
}

export interface ContentMetadata {
  filename?: string;
  fileSize?: number;
  mimeType?: string;
  url?: string;
  title?: string;
  author?: string;
  language?: string;
  uploadedAt: Date;
}

export interface ExtractedContent {
  text: string;
  keyTerms: string[];
  topics: string[];
  difficulty: string;
  readingTime: number;
  wordCount: number;
  language: string;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    hasNext?: boolean;
    hasPrev?: boolean;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  context?: string;
}

// Theme Types
export interface Theme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    success: string;
    warning: string;
    error: string;
  };
  fonts: {
    heading: string;
    body: string;
    mono: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// Utility Types
export type Timestamp = number;
export type ID = string;
export type OptionalExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Export all types as a namespace for easier imports
export namespace QuizCraft {
  export type UserType = User;
  export type QuizType = Quiz;
  export type QuestionType = Question;
  export type AttemptType = QuizAttempt;
  export type GameRoomType = GameRoom;
  export type PlayerType = Player;
  export type AnalyticsType = AnalyticsData;
  export type AchievementType = Achievement;
  export type NotificationType = Notification;
}