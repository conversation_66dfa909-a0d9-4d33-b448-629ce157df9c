import React from 'react';
import { Trophy, Medal, Award, Crown, TrendingUp } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';

interface Player {
  id: string;
  name: string;
  avatar: string;
  score: number;
  rank?: number;
  isCurrentUser?: boolean;
  accuracy?: number;
  timeSpent?: number;
  streak?: number;
}

interface LeaderboardProps {
  players: Player[];
  title?: string;
  showStats?: boolean;
  maxVisible?: number;
  className?: string;
}

export const Leaderboard: React.FC<LeaderboardProps> = ({
  players,
  title = 'Leaderboard',
  showStats = true,
  maxVisible,
  className = ''
}) => {
  // Sort players by score and assign ranks
  const sortedPlayers = [...players]
    .sort((a, b) => b.score - a.score)
    .map((player, index) => ({
      ...player,
      rank: index + 1
    }));

  const visiblePlayers = maxVisible ? sortedPlayers.slice(0, maxVisible) : sortedPlayers;

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-400" />;
      case 3:
        return <Award className="w-5 h-5 text-amber-600" />;
      default:
        return <span className="w-5 h-5 flex items-center justify-center text-sm font-bold text-gray-500">#{rank}</span>;
    }
  };

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Trophy className="w-5 h-5 text-yellow-500" />
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="space-y-1">
          {visiblePlayers.map((player) => (
            <div
              key={player.id}
              className={`flex items-center p-4 transition-colors ${
                player.isCurrentUser
                  ? 'bg-indigo-50 border-l-4 border-indigo-500'
                  : 'hover:bg-gray-50'
              }`}
            >
              {/* Rank */}
              <div className="flex-shrink-0 w-8 flex justify-center">
                {getRankIcon(player.rank!)}
              </div>

              {/* Avatar */}
              <div className="flex-shrink-0 ml-3">
                <img
                  src={player.avatar}
                  alt={player.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
              </div>

              {/* Player Info */}
              <div className="flex-1 ml-3 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className={`text-sm font-medium truncate ${
                    player.isCurrentUser ? 'text-indigo-900' : 'text-gray-900'
                  }`}>
                    {player.name}
                    {player.isCurrentUser && (
                      <span className="ml-1 text-xs text-indigo-600">(You)</span>
                    )}
                  </p>
                  
                  {player.rank && player.rank <= 3 && (
                    <span className={`px-2 py-1 text-xs font-bold rounded-full ${getRankBadgeColor(player.rank)}`}>
                      #{player.rank}
                    </span>
                  )}
                </div>

                {showStats && (
                  <div className="flex items-center space-x-4 mt-1">
                    {player.accuracy !== undefined && (
                      <span className="text-xs text-gray-500">
                        {player.accuracy}% accuracy
                      </span>
                    )}
                    {player.timeSpent !== undefined && (
                      <span className="text-xs text-gray-500">
                        {formatTime(player.timeSpent)}
                      </span>
                    )}
                    {player.streak !== undefined && player.streak > 0 && (
                      <span className="text-xs text-orange-600 flex items-center">
                        <TrendingUp className="w-3 h-3 mr-1" />
                        {player.streak} streak
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Score */}
              <div className="flex-shrink-0 text-right">
                <p className={`text-lg font-bold ${
                  player.isCurrentUser ? 'text-indigo-600' : 'text-gray-900'
                }`}>
                  {player.score.toLocaleString()}
                </p>
                <p className="text-xs text-gray-500">points</p>
              </div>
            </div>
          ))}
        </div>

        {maxVisible && players.length > maxVisible && (
          <div className="p-4 text-center border-t border-gray-200">
            <p className="text-sm text-gray-500">
              +{players.length - maxVisible} more players
            </p>
          </div>
        )}

        {visiblePlayers.length === 0 && (
          <div className="p-8 text-center">
            <Trophy className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">No players yet</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Compact leaderboard for smaller spaces
interface CompactLeaderboardProps {
  players: Player[];
  maxVisible?: number;
  className?: string;
}

export const CompactLeaderboard: React.FC<CompactLeaderboardProps> = ({
  players,
  maxVisible = 5,
  className = ''
}) => {
  const sortedPlayers = [...players]
    .sort((a, b) => b.score - a.score)
    .slice(0, maxVisible)
    .map((player, index) => ({
      ...player,
      rank: index + 1
    }));

  return (
    <div className={`space-y-2 ${className}`}>
      {sortedPlayers.map((player) => (
        <div
          key={player.id}
          className={`flex items-center p-2 rounded-lg ${
            player.isCurrentUser ? 'bg-indigo-50' : 'bg-gray-50'
          }`}
        >
          <span className="w-6 text-center text-sm font-bold text-gray-600">
            #{player.rank}
          </span>
          <img
            src={player.avatar}
            alt={player.name}
            className="w-6 h-6 rounded-full object-cover ml-2"
          />
          <span className="flex-1 ml-2 text-sm font-medium text-gray-900 truncate">
            {player.name}
          </span>
          <span className="text-sm font-bold text-gray-900">
            {player.score}
          </span>
        </div>
      ))}
    </div>
  );
};