import React, { useState, useEffect } from 'react';
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  X,
  Brain,
  Target,
  TrendingUp,
  Award,
  RefreshC<PERSON>,
  BookOpen,
  Star,
  ChevronRight
} from 'lucide-react';
import { useQuizStore } from '../store/quizStore';

interface QuizTakerProps {
  onBack: () => void;
}

interface Quiz {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: string;
  questionsCount: number;
  estimatedTime: number;
  rating: number;
  attempts: number;
  tags: string[];
}

interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  points: number;
}

const QuizTaker: React.FC<QuizTakerProps> = ({ onBack }) => {
  const [selectedQuiz, setSelectedQuiz] = useState<Quiz | null>(null);
  const [isQuizActive, setIsQuizActive] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes
  const [showResults, setShowResults] = useState(false);
  const [score, setScore] = useState(0);

  const quizzes: Quiz[] = [
    {
      id: '1',
      title: 'Advanced JavaScript Concepts',
      description: 'Deep dive into closures, prototypes, and async programming',
      category: 'Programming',
      difficulty: 'Advanced',
      questionsCount: 15,
      estimatedTime: 25,
      rating: 4.8,
      attempts: 1247,
      tags: ['JavaScript', 'Web Development', 'ES6+']
    },
    {
      id: '2',
      title: 'Machine Learning Fundamentals',
      description: 'Core concepts of ML algorithms and data science',
      category: 'AI/ML',
      difficulty: 'Intermediate',
      questionsCount: 20,
      estimatedTime: 30,
      rating: 4.6,
      attempts: 892,
      tags: ['Machine Learning', 'Data Science', 'Python']
    },
    {
      id: '3',
      title: 'React Hooks Mastery',
      description: 'Master useState, useEffect, and custom hooks',
      category: 'Frontend',
      difficulty: 'Intermediate',
      questionsCount: 12,
      estimatedTime: 20,
      rating: 4.9,
      attempts: 2156,
      tags: ['React', 'Hooks', 'Frontend']
    },
    {
      id: '4',
      title: 'Database Design Principles',
      description: 'Learn normalization, indexing, and query optimization',
      category: 'Database',
      difficulty: 'Advanced',
      questionsCount: 18,
      estimatedTime: 35,
      rating: 4.7,
      attempts: 743,
      tags: ['Database', 'SQL', 'Design Patterns']
    },
    {
      id: '5',
      title: 'TypeScript Best Practices',
      description: 'Type safety, generics, and advanced TypeScript features',
      category: 'Programming',
      difficulty: 'Intermediate',
      questionsCount: 14,
      estimatedTime: 22,
      rating: 4.8,
      attempts: 1089,
      tags: ['TypeScript', 'JavaScript', 'Type Safety']
    },
    {
      id: '6',
      title: 'System Design Fundamentals',
      description: 'Scalability, load balancing, and distributed systems',
      category: 'System Design',
      difficulty: 'Advanced',
      questionsCount: 25,
      estimatedTime: 45,
      rating: 4.5,
      attempts: 567,
      tags: ['System Design', 'Architecture', 'Scalability']
    }
  ];

  const sampleQuestions: Question[] = [
    {
      id: '1',
      type: 'multiple-choice',
      question: 'What is the primary purpose of JavaScript closures?',
      options: [
        'To create private variables and methods',
        'To improve performance',
        'To handle asynchronous operations',
        'To manage memory automatically'
      ],
      correctAnswer: 'To create private variables and methods',
      explanation: 'Closures allow functions to access variables from their outer scope even after the outer function has returned, enabling encapsulation and private variables.',
      points: 1
    },
    {
      id: '2',
      type: 'multiple-choice',
      question: 'Which React Hook is used for side effects?',
      options: ['useState', 'useEffect', 'useContext', 'useReducer'],
      correctAnswer: 'useEffect',
      explanation: 'useEffect is the Hook that handles side effects in functional components, such as data fetching, subscriptions, or manually changing the DOM.',
      points: 1
    },
    {
      id: '3',
      type: 'true-false',
      question: 'TypeScript is a superset of JavaScript that adds static typing.',
      correctAnswer: 'true',
      explanation: 'TypeScript extends JavaScript by adding static type definitions, making it easier to catch errors during development.',
      points: 1
    }
  ];

  // Timer effect
  useEffect(() => {
    if (isQuizActive && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      handleFinishQuiz();
    }
  }, [isQuizActive, timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const startQuiz = (quiz: Quiz) => {
    setSelectedQuiz(quiz);
    setIsQuizActive(true);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setTimeLeft(quiz.estimatedTime * 60);
    setShowResults(false);
  };

  const handleAnswer = (questionId: string, answer: string) => {
    setAnswers(prev => ({ ...prev, [questionId]: answer }));
  };

  const nextQuestion = () => {
    if (currentQuestionIndex < sampleQuestions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      handleFinishQuiz();
    }
  };

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleFinishQuiz = () => {
    // Calculate score
    const correctAnswers = sampleQuestions.filter(q => answers[q.id] === q.correctAnswer).length;
    const finalScore = Math.round((correctAnswers / sampleQuestions.length) * 100);
    setScore(finalScore);
    setIsQuizActive(false);
    setShowResults(true);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-700';
      case 'intermediate': return 'bg-yellow-100 text-yellow-700';
      case 'advanced': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const renderQuizSelection = () => (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Challenge</h2>
        <p className="text-gray-600">Select a quiz to test your knowledge and improve your skills</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {quizzes.map((quiz) => (
          <div key={quiz.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow group">
            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(quiz.difficulty)}`}>
                  {quiz.difficulty}
                </span>
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-600">{quiz.rating}</span>
                </div>
              </div>

              <h3 className="font-semibold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors">
                {quiz.title}
              </h3>
              <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                {quiz.description}
              </p>

              <div className="flex flex-wrap gap-1 mb-4">
                {quiz.tags.map((tag) => (
                  <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-md">
                    {tag}
                  </span>
                ))}
              </div>

              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <BookOpen className="w-4 h-4" />
                    <span>{quiz.questionsCount} questions</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-4 h-4" />
                    <span>{quiz.estimatedTime} min</span>
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500">{quiz.attempts.toLocaleString()} attempts</span>
                <button
                  onClick={() => startQuiz(quiz)}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center space-x-2"
                >
                  <span>Start Quiz</span>
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderQuizInterface = () => {
    const currentQuestion = sampleQuestions[currentQuestionIndex];
    const progress = ((currentQuestionIndex + 1) / sampleQuestions.length) * 100;

    return (
      <div className="max-w-3xl mx-auto">
        {/* Quiz Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{selectedQuiz?.title}</h2>
              <p className="text-gray-600">Question {currentQuestionIndex + 1} of {sampleQuestions.length}</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-gray-600">
                <Clock className="w-5 h-5" />
                <span className="font-mono text-lg">{formatTime(timeLeft)}</span>
              </div>
              <button
                onClick={handleFinishQuiz}
                className="px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                Finish
              </button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Question Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-6">
          <div className="mb-6">
            <div className="flex items-center space-x-2 mb-4">
              <span className="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full text-sm font-medium">
                {currentQuestion.type === 'multiple-choice' ? 'Multiple Choice' : 'True/False'}
              </span>
              <span className="text-sm text-gray-500">{currentQuestion.points} point(s)</span>
            </div>
            <h3 className="text-xl font-medium text-gray-900 leading-relaxed">
              {currentQuestion.question}
            </h3>
          </div>

          {/* Answer Options */}
          <div className="space-y-3">
            {currentQuestion.type === 'multiple-choice' && currentQuestion.options?.map((option, index) => (
              <label
                key={index}
                className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                  answers[currentQuestion.id] === option
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <input
                  type="radio"
                  name={`question-${currentQuestion.id}`}
                  value={option}
                  checked={answers[currentQuestion.id] === option}
                  onChange={(e) => handleAnswer(currentQuestion.id, e.target.value)}
                  className="sr-only"
                />
                <div className={`w-5 h-5 rounded-full border-2 mr-3 flex items-center justify-center ${
                  answers[currentQuestion.id] === option
                    ? 'border-indigo-500 bg-indigo-500'
                    : 'border-gray-300'
                }`}>
                  {answers[currentQuestion.id] === option && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <span className="text-gray-900">{option}</span>
              </label>
            ))}

            {currentQuestion.type === 'true-false' && (
              <div className="flex space-x-4">
                {['true', 'false'].map((option) => (
                  <label
                    key={option}
                    className={`flex-1 flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      answers[currentQuestion.id] === option
                        ? 'border-indigo-500 bg-indigo-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <input
                      type="radio"
                      name={`question-${currentQuestion.id}`}
                      value={option}
                      checked={answers[currentQuestion.id] === option}
                      onChange={(e) => handleAnswer(currentQuestion.id, e.target.value)}
                      className="sr-only"
                    />
                    <span className="text-lg font-medium text-gray-900 capitalize">{option}</span>
                  </label>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <button
            onClick={previousQuestion}
            disabled={currentQuestionIndex === 0}
            className="px-6 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Previous</span>
          </button>

          <div className="flex items-center space-x-2">
            {sampleQuestions.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentQuestionIndex(index)}
                className={`w-8 h-8 rounded-full text-sm font-medium transition-colors ${
                  index === currentQuestionIndex
                    ? 'bg-indigo-600 text-white'
                    : answers[sampleQuestions[index].id]
                    ? 'bg-green-100 text-green-700'
                    : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                }`}
              >
                {index + 1}
              </button>
            ))}
          </div>

          <button
            onClick={nextQuestion}
            disabled={!answers[currentQuestion.id]}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <span>{currentQuestionIndex === sampleQuestions.length - 1 ? 'Finish' : 'Next'}</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  };

  const renderResults = () => (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8 text-center">
        <div className="mb-6">
          {score >= 80 ? (
            <Award className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          ) : score >= 60 ? (
            <Target className="w-16 h-16 text-blue-500 mx-auto mb-4" />
          ) : (
            <RefreshCw className="w-16 h-16 text-gray-500 mx-auto mb-4" />
          )}
          
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Quiz Complete!</h2>
          <p className="text-gray-600">Here's how you performed</p>
        </div>

        <div className="bg-gray-50 rounded-xl p-6 mb-6">
          <div className="text-4xl font-bold text-indigo-600 mb-2">{score}%</div>
          <div className="text-sm text-gray-600 mb-4">
            {sampleQuestions.filter(q => answers[q.id] === q.correctAnswer).length} out of {sampleQuestions.length} correct
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-gray-900">{formatTime((selectedQuiz?.estimatedTime || 30) * 60 - timeLeft)}</div>
              <div className="text-xs text-gray-500">Time Taken</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">{selectedQuiz?.difficulty}</div>
              <div className="text-xs text-gray-500">Difficulty</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {score >= 80 ? 'Excellent' : score >= 60 ? 'Good' : 'Needs Work'}
              </div>
              <div className="text-xs text-gray-500">Performance</div>
            </div>
          </div>
        </div>

        <div className="flex justify-center space-x-4">
          <button
            onClick={() => {
              setSelectedQuiz(null);
              setShowResults(false);
              setIsQuizActive(false);
            }}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            Take Another Quiz
          </button>
          <button
            onClick={() => startQuiz(selectedQuiz!)}
            className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Retake Quiz</span>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={onBack}
              className="p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-3">
              <Brain className="w-8 h-8 text-indigo-600" />
              <div>
                <h1 className="text-xl font-semibold text-gray-900">Quiz Taker</h1>
                <p className="text-sm text-gray-600">Test your knowledge and skills</p>
              </div>
            </div>
          </div>

          {isQuizActive && (
            <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>Time: {formatTime(timeLeft)}</span>
              </div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4" />
                <span>Progress: {Math.round(((currentQuestionIndex + 1) / sampleQuestions.length) * 100)}%</span>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {!selectedQuiz && !showResults && renderQuizSelection()}
        {isQuizActive && renderQuizInterface()}
        {showResults && renderResults()}
      </div>
    </div>
  );
};

export default QuizTaker;