/**
 * WebSocket Service for Real-time Multiplayer and Notifications
 */

type EventCallback = (data: any) => void;
type EventType = 'room_created' | 'room_updated' | 'player_joined' | 'player_left' | 
                'game_started' | 'question_started' | 'answer_submitted' | 'game_ended' |
                'notification' | 'user_status' | 'chat_message';

interface WebSocketMessage {
  type: EventType;
  data: any;
  timestamp: number;
  id: string;
}

interface ConnectionConfig {
  url: string;
  reconnectAttempts?: number;
  reconnectDelay?: number;
  heartbeatInterval?: number;
  timeout?: number;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private eventListeners = new Map<EventType, Set<EventCallback>>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private heartbeatInterval: number;
  private heartbeatTimer: number | null = null;
  private isConnecting = false;
  private isManuallyDisconnected = false;
  private config: ConnectionConfig;
  private messageQueue: WebSocketMessage[] = [];

  constructor(config: ConnectionConfig) {
    this.config = {
      reconnectAttempts: 5,
      reconnectDelay: 3000,
      heartbeatInterval: 30000,
      timeout: 10000,
      ...config
    };
    
    this.maxReconnectAttempts = this.config.reconnectAttempts!;
    this.reconnectDelay = this.config.reconnectDelay!;
    this.heartbeatInterval = this.config.heartbeatInterval!;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve();
        return;
      }

      this.isConnecting = true;
      this.isManuallyDisconnected = false;

      try {
        this.ws = new WebSocket(this.config.url);

        const timeout = setTimeout(() => {
          if (this.ws && this.ws.readyState !== WebSocket.OPEN) {
            this.ws.close();
            reject(new Error('WebSocket connection timeout'));
          }
        }, this.config.timeout);

        this.ws.onopen = () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.processMessageQueue();
          
          console.log('WebSocket connected');
          this.emit('connection_established', { timestamp: Date.now() });
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        };

        this.ws.onclose = (event) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.stopHeartbeat();
          
          console.log('WebSocket disconnected:', event.code, event.reason);
          this.emit('connection_lost', { code: event.code, reason: event.reason });

          if (!this.isManuallyDisconnected && this.shouldReconnect(event.code)) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = (error) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          console.error('WebSocket error:', error);
          this.emit('connection_error', { error });
          
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            reject(new Error('Failed to establish WebSocket connection'));
          }
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.isManuallyDisconnected = true;
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  send(type: EventType, data: any): void {
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: Date.now(),
      id: this.generateId()
    };

    if (this.isConnected()) {
      this.ws!.send(JSON.stringify(message));
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(message);
      
      // Attempt to reconnect if not already connecting
      if (!this.isConnecting && !this.isManuallyDisconnected) {
        this.connect().catch(console.error);
      }
    }
  }

  on(eventType: EventType, callback: EventCallback): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }
    this.eventListeners.get(eventType)!.add(callback);
  }

  off(eventType: EventType, callback: EventCallback): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.delete(callback);
    }
  }

  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'connecting';
      case WebSocket.OPEN: return 'connected';
      case WebSocket.CLOSING: return 'closing';
      case WebSocket.CLOSED: return 'disconnected';
      default: return 'unknown';
    }
  }

  private handleMessage(message: WebSocketMessage): void {
    // Handle system messages
    if (message.type === 'ping') {
      this.send('pong', { timestamp: Date.now() });
      return;
    }

    // Emit to registered listeners
    this.emit(message.type, message.data);
  }

  private emit(eventType: EventType, data: any): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in ${eventType} event handler:`, error);
        }
      });
    }
  }

  private shouldReconnect(closeCode: number): boolean {
    // Don't reconnect on certain close codes
    const noReconnectCodes = [1000, 1001, 1005, 4000, 4001, 4002, 4003];
    return !noReconnectCodes.includes(closeCode) && 
           this.reconnectAttempts < this.maxReconnectAttempts;
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('max_reconnect_attempts_reached', {});
      return;
    }

    const delay = this.reconnectDelay * Math.pow(1.5, this.reconnectAttempts);
    this.reconnectAttempts++;

    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      if (!this.isManuallyDisconnected) {
        this.connect().catch(error => {
          console.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected()) {
        this.send('ping', { timestamp: Date.now() });
      }
    }, this.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private processMessageQueue(): void {
    if (this.messageQueue.length > 0) {
      console.log(`Processing ${this.messageQueue.length} queued messages`);
      
      this.messageQueue.forEach(message => {
        if (this.isConnected()) {
          this.ws!.send(JSON.stringify(message));
        }
      });
      
      this.messageQueue = [];
    }
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Multiplayer-specific WebSocket service
export class MultiplayerWebSocket extends WebSocketService {
  private currentRoomId: string | null = null;
  private playerId: string | null = null;

  constructor(url: string = 'wss://api.quizcraft.ai/multiplayer') {
    super({ url });
    this.setupMultiplayerHandlers();
  }

  joinRoom(roomId: string, playerId: string): void {
    this.currentRoomId = roomId;
    this.playerId = playerId;
    this.send('join_room', { roomId, playerId });
  }

  leaveRoom(): void {
    if (this.currentRoomId && this.playerId) {
      this.send('leave_room', { roomId: this.currentRoomId, playerId: this.playerId });
    }
    this.currentRoomId = null;
    this.playerId = null;
  }

  startGame(roomId: string): void {
    this.send('start_game', { roomId });
  }

  submitAnswer(questionId: string, answer: string): void {
    if (this.currentRoomId && this.playerId) {
      this.send('answer_submitted', {
        roomId: this.currentRoomId,
        playerId: this.playerId,
        questionId,
        answer,
        timestamp: Date.now()
      });
    }
  }

  sendChatMessage(message: string): void {
    if (this.currentRoomId && this.playerId) {
      this.send('chat_message', {
        roomId: this.currentRoomId,
        playerId: this.playerId,
        message,
        timestamp: Date.now()
      });
    }
  }

  toggleReady(): void {
    if (this.currentRoomId && this.playerId) {
      this.send('toggle_ready', {
        roomId: this.currentRoomId,
        playerId: this.playerId
      });
    }
  }

  private setupMultiplayerHandlers(): void {
    this.on('room_created', (data) => {
      console.log('Room created:', data);
    });

    this.on('player_joined', (data) => {
      console.log('Player joined:', data);
    });

    this.on('player_left', (data) => {
      console.log('Player left:', data);
    });

    this.on('game_started', (data) => {
      console.log('Game started:', data);
    });

    this.on('question_started', (data) => {
      console.log('Question started:', data);
    });

    this.on('answer_submitted', (data) => {
      console.log('Answer submitted:', data);
    });

    this.on('game_ended', (data) => {
      console.log('Game ended:', data);
    });
  }
}

// Notification WebSocket service
export class NotificationWebSocket extends WebSocketService {
  private userId: string | null = null;

  constructor(url: string = 'wss://api.quizcraft.ai/notifications') {
    super({ url });
    this.setupNotificationHandlers();
  }

  subscribe(userId: string): void {
    this.userId = userId;
    this.send('subscribe', { userId });
  }

  unsubscribe(): void {
    if (this.userId) {
      this.send('unsubscribe', { userId: this.userId });
    }
    this.userId = null;
  }

  markAsRead(notificationId: string): void {
    this.send('mark_read', { notificationId });
  }

  private setupNotificationHandlers(): void {
    this.on('notification', (data) => {
      console.log('New notification:', data);
      
      // Show browser notification if permission granted
      if (Notification.permission === 'granted') {
        new Notification(data.title, {
          body: data.message,
          icon: '/favicon.ico'
        });
      }
    });
  }
}

// Export singleton instances
export const multiplayerWS = new MultiplayerWebSocket();
export const notificationWS = new NotificationWebSocket();

// Utility function to request notification permission
export const requestNotificationPermission = async (): Promise<NotificationPermission> => {
  if (!('Notification' in window)) {
    console.warn('This browser does not support notifications');
    return 'denied';
  }

  if (Notification.permission === 'default') {
    return await Notification.requestPermission();
  }

  return Notification.permission;
};

export default WebSocketService;