/**
 * Comprehensive Logging and Monitoring Service
 * Provides structured logging, error tracking, and performance monitoring
 */

interface LogLevel {
  DEBUG: 0;
  INFO: 1;
  WARN: 2;
  ERROR: 3;
  FATAL: 4;
}

interface LogEntry {
  timestamp: string;
  level: keyof LogLevel;
  message: string;
  context?: string;
  data?: any;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  url?: string;
  stack?: string;
  errorId?: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: string;
  context?: string;
  tags?: Record<string, string>;
}

interface UserAction {
  action: string;
  timestamp: string;
  userId?: string;
  sessionId?: string;
  data?: any;
  duration?: number;
}

class Logger {
  private logLevel: keyof LogLevel;
  private sessionId: string;
  private userId?: string;
  private logBuffer: LogEntry[] = [];
  private performanceBuffer: PerformanceMetric[] = [];
  private userActionBuffer: UserAction[] = [];
  private flushInterval: NodeJS.Timeout;
  private maxBufferSize = 100;

  private readonly LOG_LEVELS: LogLevel = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3,
    FATAL: 4
  };

  constructor() {
    this.logLevel = process.env.NODE_ENV === 'production' ? 'INFO' : 'DEBUG';
    this.sessionId = this.generateSessionId();
    
    // Flush logs every 30 seconds
    this.flushInterval = setInterval(() => {
      this.flush();
    }, 30000);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flush();
    });

    // Capture unhandled errors
    window.addEventListener('error', (event) => {
      this.error('Unhandled error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled promise rejection', {
        reason: event.reason,
        stack: event.reason?.stack
      });
    });
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private shouldLog(level: keyof LogLevel): boolean {
    return this.LOG_LEVELS[level] >= this.LOG_LEVELS[this.logLevel];
  }

  private createLogEntry(
    level: keyof LogLevel,
    message: string,
    context?: string,
    data?: any
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      data,
      userId: this.userId,
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...(level === 'ERROR' || level === 'FATAL' ? {
        errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      } : {})
    };
  }

  setUserId(userId: string): void {
    this.userId = userId;
  }

  setLogLevel(level: keyof LogLevel): void {
    this.logLevel = level;
  }

  debug(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('DEBUG')) return;
    
    const entry = this.createLogEntry('DEBUG', message, context, data);
    this.addToBuffer(entry);
    
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data);
    }
  }

  info(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('INFO')) return;
    
    const entry = this.createLogEntry('INFO', message, context, data);
    this.addToBuffer(entry);
    
    if (process.env.NODE_ENV === 'development') {
      console.info(`[INFO] ${message}`, data);
    }
  }

  warn(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('WARN')) return;
    
    const entry = this.createLogEntry('WARN', message, context, data);
    this.addToBuffer(entry);
    
    console.warn(`[WARN] ${message}`, data);
  }

  error(message: string, context?: string, data?: any): void {
    if (!this.shouldLog('ERROR')) return;
    
    const entry = this.createLogEntry('ERROR', message, context, data);
    
    // Add stack trace if data contains an error
    if (data instanceof Error) {
      entry.stack = data.stack;
      entry.data = {
        name: data.name,
        message: data.message,
        ...data
      };
    }
    
    this.addToBuffer(entry);
    console.error(`[ERROR] ${message}`, data);
  }

  fatal(message: string, context?: string, data?: any): void {
    const entry = this.createLogEntry('FATAL', message, context, data);
    
    if (data instanceof Error) {
      entry.stack = data.stack;
      entry.data = {
        name: data.name,
        message: data.message,
        ...data
      };
    }
    
    this.addToBuffer(entry);
    console.error(`[FATAL] ${message}`, data);
    
    // Immediately flush fatal errors
    this.flush();
  }

  // Performance monitoring
  recordPerformance(name: string, value: number, context?: string, tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: new Date().toISOString(),
      context,
      tags
    };
    
    this.performanceBuffer.push(metric);
    
    if (this.performanceBuffer.length >= this.maxBufferSize) {
      this.flush();
    }
  }

  // User action tracking
  trackUserAction(action: string, data?: any, duration?: number): void {
    const userAction: UserAction = {
      action,
      timestamp: new Date().toISOString(),
      userId: this.userId,
      sessionId: this.sessionId,
      data,
      duration
    };
    
    this.userActionBuffer.push(userAction);
    
    if (this.userActionBuffer.length >= this.maxBufferSize) {
      this.flush();
    }
  }

  // Timing utilities
  startTimer(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.recordPerformance(name, duration, 'timer');
    };
  }

  // Measure function execution time
  measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    
    return fn().finally(() => {
      const duration = performance.now() - startTime;
      this.recordPerformance(name, duration, 'async_function');
    });
  }

  measure<T>(name: string, fn: () => T): T {
    const startTime = performance.now();
    
    try {
      const result = fn();
      const duration = performance.now() - startTime;
      this.recordPerformance(name, duration, 'function');
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordPerformance(name, duration, 'function_error');
      throw error;
    }
  }

  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    if (this.logBuffer.length >= this.maxBufferSize) {
      this.flush();
    }
  }

  private async flush(): Promise<void> {
    if (this.logBuffer.length === 0 && 
        this.performanceBuffer.length === 0 && 
        this.userActionBuffer.length === 0) {
      return;
    }

    const payload = {
      logs: [...this.logBuffer],
      performance: [...this.performanceBuffer],
      userActions: [...this.userActionBuffer],
      sessionId: this.sessionId,
      timestamp: new Date().toISOString()
    };

    // Clear buffers
    this.logBuffer = [];
    this.performanceBuffer = [];
    this.userActionBuffer = [];

    try {
      // In production, send to logging service
      if (process.env.NODE_ENV === 'production') {
        await this.sendToLoggingService(payload);
      } else {
        // In development, just log to console
        console.group('Logger Flush');
        console.log('Logs:', payload.logs);
        console.log('Performance:', payload.performance);
        console.log('User Actions:', payload.userActions);
        console.groupEnd();
      }
    } catch (error) {
      console.error('Failed to flush logs:', error);
      // Re-add to buffer for retry (but limit to prevent infinite growth)
      if (this.logBuffer.length < this.maxBufferSize) {
        this.logBuffer.push(...payload.logs.slice(-10));
      }
    }
  }

  private async sendToLoggingService(payload: any): Promise<void> {
    // This would typically send to a service like LogRocket, Datadog, etc.
    // For now, we'll use a mock implementation
    
    try {
      const response = await fetch('/api/logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`Logging service responded with ${response.status}`);
      }
    } catch (error) {
      // Fallback: store in localStorage for later retry
      try {
        const stored = localStorage.getItem('quizcraft_pending_logs') || '[]';
        const pendingLogs = JSON.parse(stored);
        pendingLogs.push(payload);
        
        // Keep only last 10 payloads to prevent storage overflow
        const limitedLogs = pendingLogs.slice(-10);
        localStorage.setItem('quizcraft_pending_logs', JSON.stringify(limitedLogs));
      } catch (storageError) {
        console.error('Failed to store logs in localStorage:', storageError);
      }
      
      throw error;
    }
  }

  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
    this.flush();
  }
}

// Create singleton instance
export const logger = new Logger();

// Export types
export type { LogEntry, PerformanceMetric, UserAction };
