import React from 'react';
import { Clock, HelpCircle } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';

interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  points: number;
  timeLimit?: number;
}

interface QuestionCardProps {
  question: Question;
  questionNumber: number;
  totalQuestions: number;
  selectedAnswer?: string;
  onAnswerSelect: (answer: string) => void;
  timeRemaining?: number;
  showExplanation?: boolean;
  isReviewMode?: boolean;
}

export const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  questionNumber,
  totalQuestions,
  selectedAnswer,
  onAnswerSelect,
  timeRemaining,
  showExplanation = false,
  isReviewMode = false
}) => {
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderOptions = () => {
    if (question.type === 'multiple-choice' && question.options) {
      return (
        <div className="space-y-3">
          {question.options.map((option, index) => {
            const isSelected = selectedAnswer === option;
            const isCorrect = option === question.correctAnswer;
            const showCorrectAnswer = showExplanation && isCorrect;
            const showIncorrectAnswer = showExplanation && isSelected && !isCorrect;

            return (
              <button
                key={index}
                onClick={() => !isReviewMode && onAnswerSelect(option)}
                disabled={isReviewMode}
                className={`w-full text-left p-4 rounded-lg border-2 transition-all duration-200 ${
                  showCorrectAnswer
                    ? 'border-green-500 bg-green-50'
                    : showIncorrectAnswer
                    ? 'border-red-500 bg-red-50'
                    : isSelected
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${isReviewMode ? 'cursor-default' : 'cursor-pointer'}`}
              >
                <div className="flex items-center">
                  <div className={`w-6 h-6 rounded-full border-2 mr-3 flex items-center justify-center ${
                    showCorrectAnswer
                      ? 'border-green-500 bg-green-500'
                      : showIncorrectAnswer
                      ? 'border-red-500 bg-red-500'
                      : isSelected
                      ? 'border-indigo-500 bg-indigo-500'
                      : 'border-gray-300'
                  }`}>
                    {(isSelected || showCorrectAnswer) && (
                      <div className="w-2 h-2 bg-white rounded-full" />
                    )}
                  </div>
                  <span className="text-gray-900">{option}</span>
                </div>
              </button>
            );
          })}
        </div>
      );
    }

    if (question.type === 'true-false') {
      return (
        <div className="flex space-x-4">
          {['true', 'false'].map((option) => {
            const isSelected = selectedAnswer === option;
            const isCorrect = option === question.correctAnswer;
            const showCorrectAnswer = showExplanation && isCorrect;
            const showIncorrectAnswer = showExplanation && isSelected && !isCorrect;

            return (
              <button
                key={option}
                onClick={() => !isReviewMode && onAnswerSelect(option)}
                disabled={isReviewMode}
                className={`flex-1 p-4 rounded-lg border-2 transition-all duration-200 ${
                  showCorrectAnswer
                    ? 'border-green-500 bg-green-50'
                    : showIncorrectAnswer
                    ? 'border-red-500 bg-red-50'
                    : isSelected
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                } ${isReviewMode ? 'cursor-default' : 'cursor-pointer'}`}
              >
                <span className="text-lg font-medium text-gray-900 capitalize">
                  {option}
                </span>
              </button>
            );
          })}
        </div>
      );
    }

    if (question.type === 'short-answer') {
      return (
        <div>
          <textarea
            value={selectedAnswer || ''}
            onChange={(e) => !isReviewMode && onAnswerSelect(e.target.value)}
            disabled={isReviewMode}
            placeholder="Type your answer here..."
            className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
            rows={4}
          />
          {showExplanation && (
            <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Sample Answer:</strong> {question.correctAnswer}
              </p>
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <span className="bg-indigo-100 text-indigo-700 px-3 py-1 rounded-full text-sm font-medium">
              Question {questionNumber} of {totalQuestions}
            </span>
            <span className="text-sm text-gray-500">
              {question.points} point{question.points !== 1 ? 's' : ''}
            </span>
          </div>
          
          {timeRemaining !== undefined && (
            <div className="flex items-center space-x-2 text-gray-600">
              <Clock className="w-4 h-4" />
              <span className="font-mono text-sm">
                {formatTime(timeRemaining)}
              </span>
            </div>
          )}
        </div>

        {/* Question */}
        <div className="mb-6">
          <h3 className="text-xl font-medium text-gray-900 leading-relaxed">
            {question.question}
          </h3>
        </div>

        {/* Options */}
        <div className="mb-6">
          {renderOptions()}
        </div>

        {/* Explanation */}
        {showExplanation && question.explanation && (
          <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <HelpCircle className="w-5 h-5 text-gray-500 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-gray-900 mb-1">Explanation</h4>
                <p className="text-gray-700 text-sm">{question.explanation}</p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};