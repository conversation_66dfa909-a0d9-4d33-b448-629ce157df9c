/**
 * Comprehensive Monitoring Service
 * Provides performance monitoring, health checks, and system metrics
 */

import { logger } from './logger';

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

interface HealthCheck {
  name: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  message?: string;
  timestamp: number;
  responseTime?: number;
}

interface SystemMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  performance: {
    navigation: PerformanceNavigationTiming | null;
    resources: PerformanceResourceTiming[];
    marks: PerformanceMark[];
    measures: PerformanceMeasure[];
  };
  network: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  } | null;
  viewport: {
    width: number;
    height: number;
    devicePixelRatio: number;
  };
}

class MonitoringService {
  private metrics: PerformanceMetric[] = [];
  private healthChecks: Map<string, HealthCheck> = new Map();
  private observers: Map<string, PerformanceObserver> = new Map();
  private isMonitoring = false;

  /**
   * Start monitoring
   */
  start(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.setupPerformanceObservers();
    this.startHealthChecks();
    this.trackPageLoad();
    this.trackUserInteractions();

    logger.info('Monitoring service started', 'monitoring');
  }

  /**
   * Stop monitoring
   */
  stop(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();

    logger.info('Monitoring service stopped', 'monitoring');
  }

  /**
   * Record a custom metric
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags
    };

    this.metrics.push(metric);
    logger.recordPerformance(name, value, 'custom', tags);

    // Keep only last 1000 metrics to prevent memory issues
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }
  }

  /**
   * Start a performance timer
   */
  startTimer(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.recordMetric(`timer.${name}`, duration, { unit: 'ms' });
    };
  }

  /**
   * Measure function execution time
   */
  measureFunction<T>(name: string, fn: () => T): T {
    const endTimer = this.startTimer(name);
    
    try {
      const result = fn();
      endTimer();
      return result;
    } catch (error) {
      endTimer();
      this.recordMetric(`error.${name}`, 1, { type: 'function_error' });
      throw error;
    }
  }

  /**
   * Measure async function execution time
   */
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const endTimer = this.startTimer(name);
    
    try {
      const result = await fn();
      endTimer();
      return result;
    } catch (error) {
      endTimer();
      this.recordMetric(`error.${name}`, 1, { type: 'async_function_error' });
      throw error;
    }
  }

  /**
   * Track page load performance
   */
  private trackPageLoad(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          this.recordMetric('page.load_time', navigation.loadEventEnd - navigation.navigationStart);
          this.recordMetric('page.dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.navigationStart);
          this.recordMetric('page.first_paint', navigation.responseStart - navigation.navigationStart);
          this.recordMetric('page.dns_lookup', navigation.domainLookupEnd - navigation.domainLookupStart);
          this.recordMetric('page.tcp_connect', navigation.connectEnd - navigation.connectStart);
          this.recordMetric('page.server_response', navigation.responseEnd - navigation.requestStart);
        }

        // Track Core Web Vitals
        this.trackCoreWebVitals();
      }, 0);
    });
  }

  /**
   * Track Core Web Vitals
   */
  private trackCoreWebVitals(): void {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordMetric('core_web_vitals.lcp', lastEntry.startTime, { unit: 'ms' });
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('lcp', lcpObserver);
      } catch (error) {
        logger.warn('LCP observer not supported', 'monitoring');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.recordMetric('core_web_vitals.fid', entry.processingStart - entry.startTime, { unit: 'ms' });
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('fid', fidObserver);
      } catch (error) {
        logger.warn('FID observer not supported', 'monitoring');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.recordMetric('core_web_vitals.cls', clsValue, { unit: 'score' });
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.set('cls', clsObserver);
      } catch (error) {
        logger.warn('CLS observer not supported', 'monitoring');
      }
    }
  }

  /**
   * Setup performance observers
   */
  private setupPerformanceObservers(): void {
    if (!('PerformanceObserver' in window)) {
      logger.warn('PerformanceObserver not supported', 'monitoring');
      return;
    }

    // Resource timing observer
    try {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: PerformanceResourceTiming) => {
          this.recordMetric('resource.load_time', entry.duration, {
            type: entry.initiatorType,
            name: entry.name.split('/').pop() || 'unknown'
          });
        });
      });
      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.set('resource', resourceObserver);
    } catch (error) {
      logger.warn('Resource observer setup failed', 'monitoring', error);
    }

    // Long task observer
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordMetric('performance.long_task', entry.duration, { unit: 'ms' });
          logger.warn('Long task detected', 'monitoring', { duration: entry.duration });
        });
      });
      longTaskObserver.observe({ entryTypes: ['longtask'] });
      this.observers.set('longtask', longTaskObserver);
    } catch (error) {
      logger.warn('Long task observer not supported', 'monitoring');
    }
  }

  /**
   * Track user interactions
   */
  private trackUserInteractions(): void {
    // Track clicks
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      
      this.recordMetric('user.click', 1, {
        element: tagName,
        id: target.id || 'none',
        class: target.className || 'none'
      });
    });

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      this.recordMetric('user.visibility_change', 1, {
        state: document.visibilityState
      });
    });

    // Track errors
    window.addEventListener('error', (event) => {
      this.recordMetric('error.javascript', 1, {
        message: event.message,
        filename: event.filename || 'unknown',
        line: event.lineno?.toString() || 'unknown'
      });
    });
  }

  /**
   * Start health checks
   */
  private startHealthChecks(): void {
    // Check API health
    this.addHealthCheck('api', async () => {
      try {
        const start = performance.now();
        const response = await fetch('/api/health', { 
          method: 'GET',
          cache: 'no-cache'
        });
        const responseTime = performance.now() - start;

        if (response.ok) {
          return {
            status: 'healthy' as const,
            message: 'API is responding',
            responseTime
          };
        } else {
          return {
            status: 'degraded' as const,
            message: `API returned ${response.status}`,
            responseTime
          };
        }
      } catch (error) {
        return {
          status: 'unhealthy' as const,
          message: 'API is not responding'
        };
      }
    });

    // Check localStorage availability
    this.addHealthCheck('localStorage', async () => {
      try {
        const testKey = 'health_check_test';
        localStorage.setItem(testKey, 'test');
        localStorage.removeItem(testKey);
        return {
          status: 'healthy' as const,
          message: 'localStorage is available'
        };
      } catch (error) {
        return {
          status: 'unhealthy' as const,
          message: 'localStorage is not available'
        };
      }
    });

    // Check WebSocket connectivity
    this.addHealthCheck('websocket', async () => {
      try {
        const ws = new WebSocket('wss://echo.websocket.org');
        
        return new Promise<{ status: 'healthy' | 'unhealthy'; message: string }>((resolve) => {
          const timeout = setTimeout(() => {
            ws.close();
            resolve({
              status: 'unhealthy',
              message: 'WebSocket connection timeout'
            });
          }, 5000);

          ws.onopen = () => {
            clearTimeout(timeout);
            ws.close();
            resolve({
              status: 'healthy',
              message: 'WebSocket connection successful'
            });
          };

          ws.onerror = () => {
            clearTimeout(timeout);
            resolve({
              status: 'unhealthy',
              message: 'WebSocket connection failed'
            });
          };
        });
      } catch (error) {
        return {
          status: 'unhealthy' as const,
          message: 'WebSocket not supported'
        };
      }
    });

    // Run health checks every 30 seconds
    setInterval(() => {
      this.runHealthChecks();
    }, 30000);

    // Run initial health checks
    this.runHealthChecks();
  }

  /**
   * Add a health check
   */
  addHealthCheck(
    name: string, 
    check: () => Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; message: string; responseTime?: number }>
  ): void {
    // Store the check function for later execution
    (this as any)[`healthCheck_${name}`] = check;
  }

  /**
   * Run all health checks
   */
  private async runHealthChecks(): Promise<void> {
    const checkNames = ['api', 'localStorage', 'websocket'];
    
    for (const name of checkNames) {
      const checkFunction = (this as any)[`healthCheck_${name}`];
      if (checkFunction) {
        try {
          const result = await checkFunction();
          this.healthChecks.set(name, {
            name,
            ...result,
            timestamp: Date.now()
          });
        } catch (error) {
          this.healthChecks.set(name, {
            name,
            status: 'unhealthy',
            message: 'Health check failed',
            timestamp: Date.now()
          });
        }
      }
    }
  }

  /**
   * Get system metrics
   */
  getSystemMetrics(): SystemMetrics {
    const memory = (performance as any).memory || { usedJSHeapSize: 0, totalJSHeapSize: 0 };
    const connection = (navigator as any).connection || null;

    return {
      memory: {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: memory.totalJSHeapSize > 0 ? (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100 : 0
      },
      performance: {
        navigation: performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming || null,
        resources: performance.getEntriesByType('resource') as PerformanceResourceTiming[],
        marks: performance.getEntriesByType('mark') as PerformanceMark[],
        measures: performance.getEntriesByType('measure') as PerformanceMeasure[]
      },
      network: connection ? {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      } : null,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
        devicePixelRatio: window.devicePixelRatio
      }
    };
  }

  /**
   * Get health status
   */
  getHealthStatus(): Map<string, HealthCheck> {
    return new Map(this.healthChecks);
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  /**
   * Clear metrics
   */
  clearMetrics(): void {
    this.metrics = [];
  }
}

// Create singleton instance
export const monitoringService = new MonitoringService();

// Auto-start monitoring in production
if (process.env.NODE_ENV === 'production') {
  monitoringService.start();
}

// Export types
export type { PerformanceMetric, HealthCheck, SystemMetrics };
