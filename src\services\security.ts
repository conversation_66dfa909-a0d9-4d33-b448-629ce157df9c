/**
 * Comprehensive Security Service
 * Provides CSRF protection, input sanitization, and secure communication
 */

import { logger } from './logger';
import { SECURITY_CONFIG } from '../utils/constants';

interface CSRFToken {
  token: string;
  timestamp: number;
  expires: number;
}

interface SecurityHeaders {
  'X-CSRF-Token'?: string;
  'X-Requested-With'?: string;
  'Content-Type'?: string;
  'Authorization'?: string;
}

class SecurityService {
  private csrfToken: CSRFToken | null = null;
  private trustedDomains: Set<string> = new Set([
    window.location.origin,
    'https://api.quizcraft.ai'
  ]);

  constructor() {
    this.initializeSecurity();
  }

  /**
   * Initialize security measures
   */
  private initializeSecurity(): void {
    this.setupCSRFProtection();
    this.setupContentSecurityPolicy();
    this.setupSecureHeaders();
    this.preventClickjacking();
    this.setupInputSanitization();

    logger.info('Security service initialized', 'security');
  }

  /**
   * Setup CSRF protection
   */
  private setupCSRFProtection(): void {
    if (!SECURITY_CONFIG.csrf.enabled) return;

    // Generate initial CSRF token
    this.generateCSRFToken();

    // Refresh token periodically
    setInterval(() => {
      this.refreshCSRFToken();
    }, 30 * 60 * 1000); // 30 minutes
  }

  /**
   * Generate CSRF token
   */
  private generateCSRFToken(): void {
    const token = this.generateSecureToken();
    const now = Date.now();
    
    this.csrfToken = {
      token,
      timestamp: now,
      expires: now + (60 * 60 * 1000) // 1 hour
    };

    // Store in secure cookie
    this.setSecureCookie(SECURITY_CONFIG.csrf.cookieName, token);
    
    logger.debug('CSRF token generated', 'security');
  }

  /**
   * Refresh CSRF token if needed
   */
  private refreshCSRFToken(): void {
    if (!this.csrfToken || Date.now() > this.csrfToken.expires) {
      this.generateCSRFToken();
    }
  }

  /**
   * Get CSRF token
   */
  getCSRFToken(): string | null {
    this.refreshCSRFToken();
    return this.csrfToken?.token || null;
  }

  /**
   * Validate CSRF token
   */
  validateCSRFToken(token: string): boolean {
    if (!this.csrfToken) return false;
    if (Date.now() > this.csrfToken.expires) return false;
    return this.csrfToken.token === token;
  }

  /**
   * Setup Content Security Policy
   */
  private setupContentSecurityPolicy(): void {
    if (!SECURITY_CONFIG.csp.enabled) return;

    const directives = SECURITY_CONFIG.csp.directives;
    const cspString = Object.entries(directives)
      .map(([key, values]) => `${this.camelToKebab(key)} ${values.join(' ')}`)
      .join('; ');

    // Set CSP via meta tag (fallback if server doesn't set it)
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = cspString;
    document.head.appendChild(meta);

    logger.debug('Content Security Policy set', 'security', { csp: cspString });
  }

  /**
   * Setup secure headers for requests
   */
  private setupSecureHeaders(): void {
    // Intercept fetch requests to add security headers
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const secureInit = this.addSecurityHeaders(init || {});
      return originalFetch(input, secureInit);
    };
  }

  /**
   * Add security headers to request
   */
  addSecurityHeaders(init: RequestInit): RequestInit {
    const headers: SecurityHeaders = {
      'X-Requested-With': 'XMLHttpRequest',
      ...init.headers
    };

    // Add CSRF token for state-changing requests
    if (init.method && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(init.method.toUpperCase())) {
      const csrfToken = this.getCSRFToken();
      if (csrfToken) {
        headers[SECURITY_CONFIG.csrf.headerName] = csrfToken;
      }
    }

    return {
      ...init,
      headers
    };
  }

  /**
   * Prevent clickjacking
   */
  private preventClickjacking(): void {
    // Check if page is in iframe
    if (window.self !== window.top) {
      // Allow only trusted domains
      try {
        const parentOrigin = document.referrer ? new URL(document.referrer).origin : '';
        if (!this.trustedDomains.has(parentOrigin)) {
          logger.warn('Potential clickjacking attempt detected', 'security', { 
            parentOrigin,
            currentOrigin: window.location.origin
          });
          
          // Break out of iframe
          window.top!.location = window.location.href;
        }
      } catch (error) {
        // Cross-origin access blocked - likely malicious iframe
        logger.error('Cross-origin iframe access blocked', 'security', error);
        window.top!.location = window.location.href;
      }
    }
  }

  /**
   * Setup input sanitization
   */
  private setupInputSanitization(): void {
    // Sanitize form inputs on submit
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      this.sanitizeFormInputs(form);
    });
  }

  /**
   * Sanitize form inputs
   */
  private sanitizeFormInputs(form: HTMLFormElement): void {
    const inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach((input) => {
      const element = input as HTMLInputElement | HTMLTextAreaElement;
      if (element.type !== 'password' && element.type !== 'file') {
        element.value = this.sanitizeInput(element.value);
      }
    });
  }

  /**
   * Sanitize user input
   */
  sanitizeInput(input: string): string {
    if (!input) return input;

    let sanitized = input;

    // Remove null bytes
    sanitized = sanitized.replace(/\0/g, '');

    // Normalize unicode
    sanitized = sanitized.normalize('NFKC');

    // Remove or escape potentially dangerous characters
    if (SECURITY_CONFIG.validation.sanitizeHtml) {
      sanitized = this.sanitizeHtml(sanitized);
    }

    // Limit length
    if (sanitized.length > SECURITY_CONFIG.validation.maxInputLength) {
      sanitized = sanitized.substring(0, SECURITY_CONFIG.validation.maxInputLength);
    }

    return sanitized;
  }

  /**
   * Sanitize HTML content
   */
  private sanitizeHtml(html: string): string {
    const allowedTags = SECURITY_CONFIG.validation.allowedTags;
    const div = document.createElement('div');
    div.innerHTML = html;

    // Remove script tags and event handlers
    const scripts = div.querySelectorAll('script');
    scripts.forEach(script => script.remove());

    // Remove dangerous attributes
    const allElements = div.querySelectorAll('*');
    allElements.forEach(element => {
      // Remove event handler attributes
      Array.from(element.attributes).forEach(attr => {
        if (attr.name.startsWith('on') || attr.name === 'javascript:') {
          element.removeAttribute(attr.name);
        }
      });

      // Remove non-allowed tags
      if (!allowedTags.includes(element.tagName.toLowerCase())) {
        element.replaceWith(...Array.from(element.childNodes));
      }
    });

    return div.innerHTML;
  }

  /**
   * Validate URL for security
   */
  validateUrl(url: string): boolean {
    if (!SECURITY_CONFIG.validation.validateUrls) return true;

    try {
      const urlObj = new URL(url);
      
      // Check protocol
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false;
      }

      // Check for trusted domains
      if (!this.trustedDomains.has(urlObj.origin)) {
        logger.warn('Untrusted domain detected', 'security', { url, origin: urlObj.origin });
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate secure random token
   */
  private generateSecureToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Set secure cookie
   */
  private setSecureCookie(name: string, value: string): void {
    const options = [
      `${name}=${value}`,
      'Path=/',
      'SameSite=Strict',
      'Secure',
      'HttpOnly'
    ];

    document.cookie = options.join('; ');
  }

  /**
   * Convert camelCase to kebab-case
   */
  private camelToKebab(str: string): string {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
  }

  /**
   * Encrypt sensitive data for storage
   */
  async encryptData(data: string, key?: string): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);
      
      // Generate or use provided key
      const keyMaterial = key ? 
        await crypto.subtle.importKey('raw', encoder.encode(key), 'PBKDF2', false, ['deriveKey']) :
        await crypto.subtle.generateKey({ name: 'AES-GCM', length: 256 }, true, ['encrypt', 'decrypt']);

      // Generate IV
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      // Encrypt data
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        keyMaterial,
        dataBuffer
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);

      // Convert to base64
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      logger.error('Encryption failed', 'security', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  async decryptData(encryptedData: string, key: string): Promise<string> {
    try {
      const encoder = new TextEncoder();
      const decoder = new TextDecoder();
      
      // Convert from base64
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      // Extract IV and encrypted data
      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);

      // Import key
      const keyMaterial = await crypto.subtle.importKey(
        'raw',
        encoder.encode(key),
        'PBKDF2',
        false,
        ['deriveKey']
      );

      // Decrypt data
      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        keyMaterial,
        encrypted
      );

      return decoder.decode(decrypted);
    } catch (error) {
      logger.error('Decryption failed', 'security', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Hash password securely
   */
  async hashPassword(password: string, salt?: string): Promise<{ hash: string; salt: string }> {
    const encoder = new TextEncoder();
    const passwordBuffer = encoder.encode(password);
    
    // Generate or use provided salt
    const saltBuffer = salt ? 
      encoder.encode(salt) : 
      crypto.getRandomValues(new Uint8Array(16));

    // Hash password
    const hashBuffer = await crypto.subtle.digest('SHA-256', 
      new Uint8Array([...passwordBuffer, ...saltBuffer])
    );

    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    const saltHex = Array.from(saltBuffer).map(b => b.toString(16).padStart(2, '0')).join('');

    return { hash: hashHex, salt: saltHex };
  }

  /**
   * Add trusted domain
   */
  addTrustedDomain(domain: string): void {
    this.trustedDomains.add(domain);
    logger.info('Trusted domain added', 'security', { domain });
  }

  /**
   * Remove trusted domain
   */
  removeTrustedDomain(domain: string): void {
    this.trustedDomains.delete(domain);
    logger.info('Trusted domain removed', 'security', { domain });
  }

  /**
   * Get security status
   */
  getSecurityStatus(): {
    csrfEnabled: boolean;
    cspEnabled: boolean;
    httpsOnly: boolean;
    trustedDomains: string[];
  } {
    return {
      csrfEnabled: SECURITY_CONFIG.csrf.enabled,
      cspEnabled: SECURITY_CONFIG.csp.enabled,
      httpsOnly: window.location.protocol === 'https:',
      trustedDomains: Array.from(this.trustedDomains)
    };
  }
}

// Create singleton instance
export const securityService = new SecurityService();

// Export types
export type { SecurityHeaders, CSRFToken };
