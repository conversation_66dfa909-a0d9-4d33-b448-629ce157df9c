/**
 * Comprehensive Error Handling Service
 * Provides centralized error handling, reporting, and user feedback
 */

import { logger } from './logger';
import { ERROR_MESSAGES } from '../utils/constants';

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  context: ErrorContext;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userAgent: string;
  url: string;
}

export interface UserFriendlyError {
  title: string;
  message: string;
  action?: {
    label: string;
    handler: () => void;
  };
  severity: 'info' | 'warning' | 'error';
}

class ErrorHandler {
  private errorReports: Map<string, ErrorReport> = new Map();
  private errorCallbacks: Array<(error: UserFriendlyError) => void> = [];

  /**
   * Handle and report an error
   */
  handleError(
    error: Error | string,
    context: ErrorContext = {},
    showToUser: boolean = true
  ): string {
    const errorId = this.generateErrorId();
    const errorMessage = typeof error === 'string' ? error : error.message;
    const stack = typeof error === 'string' ? undefined : error.stack;

    // Create error report
    const report: ErrorReport = {
      id: errorId,
      message: errorMessage,
      stack,
      context,
      timestamp: new Date(),
      severity: this.determineSeverity(errorMessage, context),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    // Store report
    this.errorReports.set(errorId, report);

    // Log error
    logger.error('Error handled', 'error-handler', {
      errorId,
      message: errorMessage,
      context,
      stack
    });

    // Send to external error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToErrorService(report);
    }

    // Show user-friendly error if requested
    if (showToUser) {
      const userError = this.createUserFriendlyError(errorMessage, context);
      this.notifyUser(userError);
    }

    return errorId;
  }

  /**
   * Handle API errors specifically
   */
  handleApiError(
    response: { status: number; statusText: string; data?: any },
    context: ErrorContext = {}
  ): string {
    let errorMessage = ERROR_MESSAGES.SERVER_ERROR;
    let severity: ErrorReport['severity'] = 'medium';

    switch (response.status) {
      case 400:
        errorMessage = response.data?.message || ERROR_MESSAGES.VALIDATION_ERROR;
        severity = 'low';
        break;
      case 401:
        errorMessage = ERROR_MESSAGES.UNAUTHORIZED;
        severity = 'medium';
        break;
      case 403:
        errorMessage = ERROR_MESSAGES.FORBIDDEN;
        severity = 'medium';
        break;
      case 404:
        errorMessage = ERROR_MESSAGES.NOT_FOUND;
        severity = 'low';
        break;
      case 429:
        errorMessage = ERROR_MESSAGES.RATE_LIMIT_EXCEEDED;
        severity = 'medium';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorMessage = ERROR_MESSAGES.SERVER_ERROR;
        severity = 'high';
        break;
      default:
        errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        severity = 'medium';
    }

    return this.handleError(
      new Error(errorMessage),
      {
        ...context,
        action: 'api_request',
        metadata: {
          status: response.status,
          statusText: response.statusText,
          responseData: response.data
        }
      }
    );
  }

  /**
   * Handle network errors
   */
  handleNetworkError(context: ErrorContext = {}): string {
    return this.handleError(
      new Error(ERROR_MESSAGES.NETWORK_ERROR),
      {
        ...context,
        action: 'network_request'
      }
    );
  }

  /**
   * Handle validation errors
   */
  handleValidationError(
    fieldErrors: Record<string, string>,
    context: ErrorContext = {}
  ): string {
    const errorMessage = Object.values(fieldErrors).join('; ');
    
    return this.handleError(
      new Error(errorMessage),
      {
        ...context,
        action: 'validation',
        metadata: { fieldErrors }
      },
      false // Don't show generic error, let form handle field-specific errors
    );
  }

  /**
   * Handle timeout errors
   */
  handleTimeoutError(context: ErrorContext = {}): string {
    return this.handleError(
      new Error(ERROR_MESSAGES.TIMEOUT_ERROR),
      {
        ...context,
        action: 'timeout'
      }
    );
  }

  /**
   * Handle permission errors
   */
  handlePermissionError(context: ErrorContext = {}): string {
    return this.handleError(
      new Error(ERROR_MESSAGES.INSUFFICIENT_PERMISSIONS),
      {
        ...context,
        action: 'permission_check'
      }
    );
  }

  /**
   * Handle rate limiting errors
   */
  handleRateLimitError(
    retryAfter?: number,
    context: ErrorContext = {}
  ): string {
    const message = retryAfter 
      ? `${ERROR_MESSAGES.RATE_LIMIT_EXCEEDED} Please try again in ${retryAfter} seconds.`
      : ERROR_MESSAGES.RATE_LIMIT_EXCEEDED;

    return this.handleError(
      new Error(message),
      {
        ...context,
        action: 'rate_limit',
        metadata: { retryAfter }
      }
    );
  }

  /**
   * Register error callback for user notifications
   */
  onError(callback: (error: UserFriendlyError) => void): void {
    this.errorCallbacks.push(callback);
  }

  /**
   * Remove error callback
   */
  offError(callback: (error: UserFriendlyError) => void): void {
    const index = this.errorCallbacks.indexOf(callback);
    if (index > -1) {
      this.errorCallbacks.splice(index, 1);
    }
  }

  /**
   * Get error report by ID
   */
  getErrorReport(errorId: string): ErrorReport | undefined {
    return this.errorReports.get(errorId);
  }

  /**
   * Get all error reports
   */
  getAllErrorReports(): ErrorReport[] {
    return Array.from(this.errorReports.values());
  }

  /**
   * Clear error reports
   */
  clearErrorReports(): void {
    this.errorReports.clear();
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(
    message: string,
    context: ErrorContext
  ): ErrorReport['severity'] {
    // Critical errors
    if (message.includes('authentication') || 
        message.includes('security') ||
        context.action === 'payment') {
      return 'critical';
    }

    // High severity errors
    if (message.includes('server') || 
        message.includes('database') ||
        message.includes('500')) {
      return 'high';
    }

    // Medium severity errors
    if (message.includes('network') || 
        message.includes('timeout') ||
        message.includes('rate limit')) {
      return 'medium';
    }

    // Low severity errors (validation, user input, etc.)
    return 'low';
  }

  /**
   * Create user-friendly error message
   */
  private createUserFriendlyError(
    message: string,
    context: ErrorContext
  ): UserFriendlyError {
    // Map technical errors to user-friendly messages
    const userFriendlyMessages: Record<string, UserFriendlyError> = {
      [ERROR_MESSAGES.NETWORK_ERROR]: {
        title: 'Connection Problem',
        message: 'Please check your internet connection and try again.',
        severity: 'error',
        action: {
          label: 'Retry',
          handler: () => window.location.reload()
        }
      },
      [ERROR_MESSAGES.SERVER_ERROR]: {
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again in a few minutes.',
        severity: 'error'
      },
      [ERROR_MESSAGES.UNAUTHORIZED]: {
        title: 'Authentication Required',
        message: 'Please log in to continue.',
        severity: 'warning',
        action: {
          label: 'Log In',
          handler: () => window.location.href = '/login'
        }
      },
      [ERROR_MESSAGES.FORBIDDEN]: {
        title: 'Access Denied',
        message: 'You don\'t have permission to perform this action.',
        severity: 'warning'
      },
      [ERROR_MESSAGES.NOT_FOUND]: {
        title: 'Not Found',
        message: 'The requested resource could not be found.',
        severity: 'info'
      },
      [ERROR_MESSAGES.RATE_LIMIT_EXCEEDED]: {
        title: 'Too Many Requests',
        message: 'You\'re doing that too often. Please wait a moment and try again.',
        severity: 'warning'
      }
    };

    // Return specific user-friendly error or generic one
    return userFriendlyMessages[message] || {
      title: 'Something Went Wrong',
      message: 'An unexpected error occurred. Please try again.',
      severity: 'error'
    };
  }

  /**
   * Notify user of error
   */
  private notifyUser(error: UserFriendlyError): void {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        console.error('Error in error callback:', err);
      }
    });
  }

  /**
   * Send error to external service
   */
  private async sendToErrorService(report: ErrorReport): Promise<void> {
    try {
      // In production, send to error reporting service like Sentry
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(report),
      });
    } catch (error) {
      // Fallback: store in localStorage for later retry
      try {
        const stored = localStorage.getItem('pending_error_reports') || '[]';
        const pendingReports = JSON.parse(stored);
        pendingReports.push(report);
        
        // Keep only last 10 reports to prevent storage overflow
        const limitedReports = pendingReports.slice(-10);
        localStorage.setItem('pending_error_reports', JSON.stringify(limitedReports));
      } catch (storageError) {
        console.error('Failed to store error report:', storageError);
      }
    }
  }
}

// Create singleton instance
export const errorHandler = new ErrorHandler();

// Global error handlers
window.addEventListener('error', (event) => {
  errorHandler.handleError(event.error || event.message, {
    component: 'global',
    action: 'unhandled_error',
    metadata: {
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    }
  });
});

window.addEventListener('unhandledrejection', (event) => {
  errorHandler.handleError(event.reason, {
    component: 'global',
    action: 'unhandled_promise_rejection'
  });
});

// Export types
export type { ErrorContext, ErrorReport, UserFriendlyError };
