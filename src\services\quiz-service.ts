/**
 * Comprehensive Quiz Management Service
 * Handles quiz CRUD operations, search, filtering, templates, and advanced features
 */

import { apiClient } from './api';
import { logger } from './logger';
import { rateLimiter } from './rate-limiter';
import { Quiz, Question, QuizAttempt, QuizTemplate } from '../types';

interface QuizFilters {
  category?: string;
  difficulty?: string;
  tags?: string[];
  author?: string;
  isPublic?: boolean;
  minRating?: number;
  maxDuration?: number;
  questionCount?: { min?: number; max?: number };
  createdAfter?: Date;
  createdBefore?: Date;
}

interface QuizSearchOptions {
  query?: string;
  filters?: QuizFilters;
  sortBy?: 'title' | 'createdAt' | 'rating' | 'attempts' | 'difficulty';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

interface QuizStats {
  totalQuizzes: number;
  totalAttempts: number;
  averageScore: number;
  popularCategories: Array<{ category: string; count: number }>;
  topRatedQuizzes: Quiz[];
  recentQuizzes: Quiz[];
}

class QuizService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Get cached data if available and not expired
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Store data in cache
   */
  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Create a new quiz
   */
  async createQuiz(quizData: Partial<Quiz>, userId: string): Promise<Quiz> {
    // Check rate limiting
    const rateLimitResult = rateLimiter.checkQuizCreationLimit(userId);
    if (!rateLimitResult.allowed) {
      throw new Error(rateLimitResult.message || 'Quiz creation rate limit exceeded');
    }

    try {
      logger.info('Creating quiz', 'quiz-service', { userId, title: quizData.title });

      const response = await apiClient.createQuiz({
        ...quizData,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        stats: {
          totalAttempts: 0,
          averageScore: 0,
          averageTime: 0,
          passRate: 0,
          popularityScore: 0
        }
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to create quiz');
      }

      logger.trackUserAction('create_quiz', { quizId: response.data.id, title: quizData.title });
      
      // Clear relevant caches
      this.clearUserQuizzesCache(userId);
      
      return response.data;
    } catch (error) {
      logger.error('Quiz creation failed', 'quiz-service', { userId, error });
      throw error;
    }
  }

  /**
   * Update an existing quiz
   */
  async updateQuiz(quizId: string, updates: Partial<Quiz>, userId: string): Promise<Quiz> {
    try {
      logger.info('Updating quiz', 'quiz-service', { userId, quizId });

      const response = await apiClient.updateQuiz(quizId, {
        ...updates,
        updatedAt: new Date()
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to update quiz');
      }

      logger.trackUserAction('update_quiz', { quizId, updates: Object.keys(updates) });
      
      // Clear caches
      this.cache.delete(`quiz:${quizId}`);
      this.clearUserQuizzesCache(userId);
      
      return response.data;
    } catch (error) {
      logger.error('Quiz update failed', 'quiz-service', { userId, quizId, error });
      throw error;
    }
  }

  /**
   * Delete a quiz
   */
  async deleteQuiz(quizId: string, userId: string): Promise<void> {
    try {
      logger.info('Deleting quiz', 'quiz-service', { userId, quizId });

      const response = await apiClient.deleteQuiz(quizId);

      if (!response.success) {
        throw new Error(response.error || 'Failed to delete quiz');
      }

      logger.trackUserAction('delete_quiz', { quizId });
      
      // Clear caches
      this.cache.delete(`quiz:${quizId}`);
      this.clearUserQuizzesCache(userId);
      
    } catch (error) {
      logger.error('Quiz deletion failed', 'quiz-service', { userId, quizId, error });
      throw error;
    }
  }

  /**
   * Get a quiz by ID
   */
  async getQuiz(quizId: string): Promise<Quiz> {
    const cacheKey = `quiz:${quizId}`;
    const cached = this.getFromCache<Quiz>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.getQuizById(quizId);

      if (!response.success) {
        throw new Error(response.error || 'Quiz not found');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get quiz', 'quiz-service', { quizId, error });
      throw error;
    }
  }

  /**
   * Search and filter quizzes
   */
  async searchQuizzes(options: QuizSearchOptions = {}): Promise<{
    quizzes: Quiz[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const cacheKey = `search:${JSON.stringify(options)}`;
    const cached = this.getFromCache<any>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const queryParams = this.buildSearchParams(options);
      const response = await apiClient.request(`/quizzes/search?${queryParams}`);

      if (!response.success) {
        throw new Error(response.error || 'Search failed');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Quiz search failed', 'quiz-service', { options, error });
      throw error;
    }
  }

  /**
   * Get quizzes by user
   */
  async getUserQuizzes(userId: string, includePrivate = false): Promise<Quiz[]> {
    const cacheKey = `user-quizzes:${userId}:${includePrivate}`;
    const cached = this.getFromCache<Quiz[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.getQuizzes({
        createdBy: userId,
        includePrivate
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to get user quizzes');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get user quizzes', 'quiz-service', { userId, error });
      throw error;
    }
  }

  /**
   * Get featured quizzes
   */
  async getFeaturedQuizzes(limit = 10): Promise<Quiz[]> {
    const cacheKey = `featured:${limit}`;
    const cached = this.getFromCache<Quiz[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request(`/quizzes/featured?limit=${limit}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get featured quizzes');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get featured quizzes', 'quiz-service', { error });
      throw error;
    }
  }

  /**
   * Get popular quizzes
   */
  async getPopularQuizzes(limit = 10, timeframe = '7d'): Promise<Quiz[]> {
    const cacheKey = `popular:${limit}:${timeframe}`;
    const cached = this.getFromCache<Quiz[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request(`/quizzes/popular?limit=${limit}&timeframe=${timeframe}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get popular quizzes');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get popular quizzes', 'quiz-service', { error });
      throw error;
    }
  }

  /**
   * Get quiz categories
   */
  async getCategories(): Promise<Array<{ name: string; count: number; description?: string }>> {
    const cacheKey = 'categories';
    const cached = this.getFromCache<any>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request('/quizzes/categories');

      if (!response.success) {
        throw new Error(response.error || 'Failed to get categories');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get categories', 'quiz-service', { error });
      throw error;
    }
  }

  /**
   * Get quiz statistics
   */
  async getQuizStats(): Promise<QuizStats> {
    const cacheKey = 'quiz-stats';
    const cached = this.getFromCache<QuizStats>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request('/quizzes/stats');

      if (!response.success) {
        throw new Error(response.error || 'Failed to get quiz stats');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get quiz stats', 'quiz-service', { error });
      throw error;
    }
  }

  /**
   * Duplicate a quiz
   */
  async duplicateQuiz(quizId: string, userId: string, newTitle?: string): Promise<Quiz> {
    try {
      const originalQuiz = await this.getQuiz(quizId);
      
      const duplicatedQuiz = {
        ...originalQuiz,
        id: undefined, // Will be generated by server
        title: newTitle || `${originalQuiz.title} (Copy)`,
        createdBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        isPublic: false, // Duplicated quizzes are private by default
        stats: {
          totalAttempts: 0,
          averageScore: 0,
          averageTime: 0,
          passRate: 0,
          popularityScore: 0
        }
      };

      return await this.createQuiz(duplicatedQuiz, userId);
    } catch (error) {
      logger.error('Quiz duplication failed', 'quiz-service', { quizId, userId, error });
      throw error;
    }
  }

  /**
   * Build search query parameters
   */
  private buildSearchParams(options: QuizSearchOptions): string {
    const params = new URLSearchParams();

    if (options.query) params.append('q', options.query);
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortOrder) params.append('sortOrder', options.sortOrder);
    if (options.page) params.append('page', options.page.toString());
    if (options.limit) params.append('limit', options.limit.toString());

    if (options.filters) {
      const filters = options.filters;
      if (filters.category) params.append('category', filters.category);
      if (filters.difficulty) params.append('difficulty', filters.difficulty);
      if (filters.tags) filters.tags.forEach(tag => params.append('tags', tag));
      if (filters.author) params.append('author', filters.author);
      if (filters.isPublic !== undefined) params.append('isPublic', filters.isPublic.toString());
      if (filters.minRating) params.append('minRating', filters.minRating.toString());
      if (filters.maxDuration) params.append('maxDuration', filters.maxDuration.toString());
      if (filters.questionCount?.min) params.append('minQuestions', filters.questionCount.min.toString());
      if (filters.questionCount?.max) params.append('maxQuestions', filters.questionCount.max.toString());
      if (filters.createdAfter) params.append('createdAfter', filters.createdAfter.toISOString());
      if (filters.createdBefore) params.append('createdBefore', filters.createdBefore.toISOString());
    }

    return params.toString();
  }

  /**
   * Clear user quizzes cache
   */
  private clearUserQuizzesCache(userId: string): void {
    for (const key of this.cache.keys()) {
      if (key.startsWith(`user-quizzes:${userId}`)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get quiz templates
   */
  async getTemplates(category?: string): Promise<QuizTemplate[]> {
    const cacheKey = `templates:${category || 'all'}`;
    const cached = this.getFromCache<QuizTemplate[]>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = category ? `?category=${category}` : '';
      const response = await apiClient.request(`/quizzes/templates${params}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get templates');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get templates', 'quiz-service', { category, error });
      throw error;
    }
  }

  /**
   * Create quiz from template
   */
  async createFromTemplate(templateId: string, userId: string, customizations?: Partial<Quiz>): Promise<Quiz> {
    try {
      logger.info('Creating quiz from template', 'quiz-service', { userId, templateId });

      const response = await apiClient.request('/quizzes/from-template', {
        method: 'POST',
        body: {
          templateId,
          userId,
          customizations
        }
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to create quiz from template');
      }

      logger.trackUserAction('create_from_template', { templateId, quizId: response.data.id });

      // Clear relevant caches
      this.clearUserQuizzesCache(userId);

      return response.data;
    } catch (error) {
      logger.error('Failed to create quiz from template', 'quiz-service', { userId, templateId, error });
      throw error;
    }
  }

  /**
   * Save quiz as template
   */
  async saveAsTemplate(quizId: string, templateData: Partial<QuizTemplate>, userId: string): Promise<QuizTemplate> {
    try {
      logger.info('Saving quiz as template', 'quiz-service', { userId, quizId });

      const response = await apiClient.request('/quizzes/save-as-template', {
        method: 'POST',
        body: {
          quizId,
          templateData,
          userId
        }
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to save quiz as template');
      }

      logger.trackUserAction('save_as_template', { quizId, templateId: response.data.id });

      // Clear templates cache
      this.cache.delete('templates:all');
      if (templateData.category) {
        this.cache.delete(`templates:${templateData.category}`);
      }

      return response.data;
    } catch (error) {
      logger.error('Failed to save quiz as template', 'quiz-service', { userId, quizId, error });
      throw error;
    }
  }

  /**
   * Rate a quiz
   */
  async rateQuiz(quizId: string, rating: number, userId: string): Promise<void> {
    if (rating < 1 || rating > 5) {
      throw new Error('Rating must be between 1 and 5');
    }

    try {
      const response = await apiClient.request(`/quizzes/${quizId}/rate`, {
        method: 'POST',
        body: { rating, userId }
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to rate quiz');
      }

      logger.trackUserAction('rate_quiz', { quizId, rating });

      // Clear quiz cache to refresh rating
      this.cache.delete(`quiz:${quizId}`);

    } catch (error) {
      logger.error('Failed to rate quiz', 'quiz-service', { userId, quizId, rating, error });
      throw error;
    }
  }

  /**
   * Report a quiz
   */
  async reportQuiz(quizId: string, reason: string, description: string, userId: string): Promise<void> {
    try {
      const response = await apiClient.request(`/quizzes/${quizId}/report`, {
        method: 'POST',
        body: { reason, description, userId }
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to report quiz');
      }

      logger.trackUserAction('report_quiz', { quizId, reason });

    } catch (error) {
      logger.error('Failed to report quiz', 'quiz-service', { userId, quizId, reason, error });
      throw error;
    }
  }

  /**
   * Get quiz analytics
   */
  async getQuizAnalytics(quizId: string, userId: string): Promise<any> {
    const cacheKey = `analytics:${quizId}`;
    const cached = this.getFromCache<any>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request(`/quizzes/${quizId}/analytics`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get quiz analytics');
      }

      // Cache for shorter time since analytics change frequently
      this.cache.set(cacheKey, { data: response.data, timestamp: Date.now() });

      return response.data;
    } catch (error) {
      logger.error('Failed to get quiz analytics', 'quiz-service', { userId, quizId, error });
      throw error;
    }
  }

  /**
   * Clear all caches
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Create singleton instance
export const quizService = new QuizService();

// Export types
export type { QuizFilters, QuizSearchOptions, QuizStats };
