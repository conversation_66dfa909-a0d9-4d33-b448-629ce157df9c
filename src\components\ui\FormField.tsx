/**
 * Form Field Component with Validation
 * Provides comprehensive form field functionality with validation, error handling, and accessibility
 */

import React, { useState, useCallback, useEffect } from 'react';
import { Eye, EyeOff, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { Input } from './Input';

interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: string) => string | null;
  email?: boolean;
  url?: boolean;
  number?: boolean;
  min?: number;
  max?: number;
}

interface FormFieldProps {
  name: string;
  label: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea';
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  validation?: ValidationRule;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  helpText?: string;
  className?: string;
  autoComplete?: string;
  rows?: number; // for textarea
  showPasswordToggle?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  error: string | null;
}

export const FormField: React.FC<FormFieldProps> = ({
  name,
  label,
  type = 'text',
  value,
  onChange,
  onBlur,
  validation,
  placeholder,
  disabled = false,
  required = false,
  helpText,
  className = '',
  autoComplete,
  rows = 3,
  showPasswordToggle = false,
  validateOnChange = false,
  validateOnBlur = true
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [touched, setTouched] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    error: null
  });

  const validateValue = useCallback((val: string): ValidationResult => {
    if (!validation) {
      return { isValid: true, error: null };
    }

    // Required validation
    if (validation.required && (!val || val.trim() === '')) {
      return { isValid: false, error: `${label} is required` };
    }

    // Skip other validations if value is empty and not required
    if (!val || val.trim() === '') {
      return { isValid: true, error: null };
    }

    // Length validations
    if (validation.minLength && val.length < validation.minLength) {
      return { 
        isValid: false, 
        error: `${label} must be at least ${validation.minLength} characters` 
      };
    }

    if (validation.maxLength && val.length > validation.maxLength) {
      return { 
        isValid: false, 
        error: `${label} must be no more than ${validation.maxLength} characters` 
      };
    }

    // Email validation
    if (validation.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(val)) {
        return { isValid: false, error: 'Please enter a valid email address' };
      }
    }

    // URL validation
    if (validation.url) {
      try {
        new URL(val);
      } catch {
        return { isValid: false, error: 'Please enter a valid URL' };
      }
    }

    // Number validation
    if (validation.number) {
      const num = parseFloat(val);
      if (isNaN(num)) {
        return { isValid: false, error: 'Please enter a valid number' };
      }

      if (validation.min !== undefined && num < validation.min) {
        return { isValid: false, error: `Value must be at least ${validation.min}` };
      }

      if (validation.max !== undefined && num > validation.max) {
        return { isValid: false, error: `Value must be no more than ${validation.max}` };
      }
    }

    // Pattern validation
    if (validation.pattern && !validation.pattern.test(val)) {
      return { isValid: false, error: `${label} format is invalid` };
    }

    // Custom validation
    if (validation.custom) {
      const customError = validation.custom(val);
      if (customError) {
        return { isValid: false, error: customError };
      }
    }

    return { isValid: true, error: null };
  }, [validation, label]);

  const handleChange = (newValue: string) => {
    onChange(newValue);

    if (validateOnChange || touched) {
      const result = validateValue(newValue);
      setValidationResult(result);
    }
  };

  const handleBlur = () => {
    setTouched(true);
    
    if (validateOnBlur) {
      const result = validateValue(value);
      setValidationResult(result);
    }

    if (onBlur) {
      onBlur();
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Validate on mount if value exists
  useEffect(() => {
    if (value && validation) {
      const result = validateValue(value);
      setValidationResult(result);
    }
  }, [value, validation, validateValue]);

  const inputType = type === 'password' && showPassword ? 'text' : type;
  const hasError = touched && !validationResult.isValid;
  const hasSuccess = touched && validationResult.isValid && value.length > 0;

  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const helpId = `${fieldId}-help`;

  const inputProps = {
    id: fieldId,
    name,
    type: inputType,
    value,
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => 
      handleChange(e.target.value),
    onBlur: handleBlur,
    placeholder,
    disabled,
    autoComplete,
    'aria-invalid': hasError,
    'aria-describedby': [
      hasError ? errorId : null,
      helpText ? helpId : null
    ].filter(Boolean).join(' ') || undefined,
    className: `${className} ${
      hasError 
        ? 'border-red-500 focus:border-red-500 focus:ring-red-500' 
        : hasSuccess 
        ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
        : ''
    }`
  };

  return (
    <div className="space-y-1">
      <label 
        htmlFor={fieldId}
        className="block text-sm font-medium text-gray-700"
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>

      <div className="relative">
        {type === 'textarea' ? (
          <textarea
            {...inputProps}
            rows={rows}
            className={`block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm ${inputProps.className}`}
          />
        ) : (
          <Input
            {...inputProps}
            className={inputProps.className}
          />
        )}

        {/* Password toggle button */}
        {type === 'password' && showPasswordToggle && (
          <button
            type="button"
            onClick={togglePasswordVisibility}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4 text-gray-400" />
            ) : (
              <Eye className="h-4 w-4 text-gray-400" />
            )}
          </button>
        )}

        {/* Validation icons */}
        {touched && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            {hasError ? (
              <AlertCircle className="h-4 w-4 text-red-500" />
            ) : hasSuccess ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : null}
          </div>
        )}
      </div>

      {/* Error message */}
      {hasError && (
        <p id={errorId} className="text-sm text-red-600 flex items-center">
          <AlertCircle className="h-4 w-4 mr-1" />
          {validationResult.error}
        </p>
      )}

      {/* Help text */}
      {helpText && !hasError && (
        <p id={helpId} className="text-sm text-gray-500 flex items-center">
          <Info className="h-4 w-4 mr-1" />
          {helpText}
        </p>
      )}
    </div>
  );
};

// Validation helper functions
export const createValidation = (rules: ValidationRule): ValidationRule => rules;

export const commonValidations = {
  required: { required: true },
  email: { required: true, email: true },
  password: { 
    required: true, 
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    custom: (value: string) => {
      if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter';
      if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter';
      if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number';
      if (!/(?=.*[@$!%*?&])/.test(value)) return 'Password must contain at least one special character';
      return null;
    }
  },
  url: { url: true },
  phone: { 
    pattern: /^\+?[\d\s\-\(\)]{10,}$/,
    custom: (value: string) => {
      const cleaned = value.replace(/\D/g, '');
      if (cleaned.length < 10) return 'Phone number must be at least 10 digits';
      return null;
    }
  }
};

export default FormField;
