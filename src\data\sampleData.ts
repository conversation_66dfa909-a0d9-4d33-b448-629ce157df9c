export interface Quiz {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questions: Question[];
  createdBy: string;
  createdAt: string;
  tags: string[];
  isPublic: boolean;
  stats: {
    totalAttempts: number;
    averageScore: number;
    completionRate: number;
  };
}

export interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer';
  question: string;
  options?: string[];
  correctAnswer: string | number;
  explanation?: string;
  points: number;
}

export const sampleQuizzes: Quiz[] = [
  {
    id: '1',
    title: 'JavaScript Fundamentals',
    description: 'Test your knowledge of JavaScript basics including variables, functions, and control structures.',
    category: 'Programming',
    difficulty: 'easy',
    createdBy: 'Alex Chen',
    createdAt: '2024-01-15',
    tags: ['javascript', 'programming', 'web-development'],
    isPublic: true,
    stats: {
      totalAttempts: 1247,
      averageScore: 78,
      completionRate: 89
    },
    questions: [
      {
        id: '1-1',
        type: 'multiple-choice',
        question: 'Which of the following is the correct way to declare a variable in JavaScript?',
        options: ['var myVar = 5;', 'variable myVar = 5;', 'v myVar = 5;', 'declare myVar = 5;'],
        correctAnswer: 0,
        explanation: 'The "var" keyword is used to declare variables in JavaScript.',
        points: 10
      },
      {
        id: '1-2',
        type: 'multiple-choice',
        question: 'What does the "typeof" operator return for an array?',
        options: ['array', 'object', 'list', 'undefined'],
        correctAnswer: 1,
        explanation: 'In JavaScript, arrays are actually objects, so typeof returns "object".',
        points: 10
      },
      {
        id: '1-3',
        type: 'true-false',
        question: 'JavaScript is a statically typed language.',
        correctAnswer: 'false',
        explanation: 'JavaScript is dynamically typed, meaning variable types are determined at runtime.',
        points: 10
      }
    ]
  },
  {
    id: '2',
    title: 'React Hooks Deep Dive',
    description: 'Advanced concepts in React Hooks including useEffect, useContext, and custom hooks.',
    category: 'Programming',
    difficulty: 'hard',
    createdBy: 'Sarah Johnson',
    createdAt: '2024-01-20',
    tags: ['react', 'hooks', 'frontend'],
    isPublic: true,
    stats: {
      totalAttempts: 892,
      averageScore: 65,
      completionRate: 72
    },
    questions: [
      {
        id: '2-1',
        type: 'multiple-choice',
        question: 'When does useEffect run by default?',
        options: ['Before render', 'After every render', 'Only on mount', 'Only on unmount'],
        correctAnswer: 1,
        explanation: 'useEffect runs after every completed render by default.',
        points: 15
      },
      {
        id: '2-2',
        type: 'multiple-choice',
        question: 'What is the correct way to create a custom hook?',
        options: [
          'function myHook() {}',
          'const myHook = () => {}',
          'function useMyHook() {}',
          'hook useMyHook() {}'
        ],
        correctAnswer: 2,
        explanation: 'Custom hooks must start with "use" to follow React conventions.',
        points: 15
      }
    ]
  },
  {
    id: '3',
    title: 'World Geography Quiz',
    description: 'Test your knowledge of world capitals, countries, and geographical features.',
    category: 'Geography',
    difficulty: 'medium',
    createdBy: 'Mike Wilson',
    createdAt: '2024-01-18',
    tags: ['geography', 'world', 'capitals'],
    isPublic: true,
    stats: {
      totalAttempts: 2156,
      averageScore: 72,
      completionRate: 94
    },
    questions: [
      {
        id: '3-1',
        type: 'multiple-choice',
        question: 'What is the capital of Australia?',
        options: ['Sydney', 'Melbourne', 'Canberra', 'Perth'],
        correctAnswer: 2,
        explanation: 'Canberra is the capital city of Australia, not Sydney or Melbourne.',
        points: 10
      },
      {
        id: '3-2',
        type: 'multiple-choice',
        question: 'Which is the longest river in the world?',
        options: ['Amazon River', 'Nile River', 'Mississippi River', 'Yangtze River'],
        correctAnswer: 1,
        explanation: 'The Nile River is generally considered the longest river in the world.',
        points: 10
      },
      {
        id: '3-3',
        type: 'true-false',
        question: 'Mount Everest is located entirely in Nepal.',
        correctAnswer: 'false',
        explanation: 'Mount Everest is located on the border between Nepal and Tibet (China).',
        points: 10
      }
    ]
  },
  {
    id: '4',
    title: 'Basic Mathematics',
    description: 'Fundamental math concepts including algebra, geometry, and arithmetic.',
    category: 'Mathematics',
    difficulty: 'easy',
    createdBy: 'Emma Davis',
    createdAt: '2024-01-22',
    tags: ['math', 'algebra', 'geometry'],
    isPublic: true,
    stats: {
      totalAttempts: 3421,
      averageScore: 84,
      completionRate: 96
    },
    questions: [
      {
        id: '4-1',
        type: 'multiple-choice',
        question: 'What is 15% of 200?',
        options: ['25', '30', '35', '40'],
        correctAnswer: 1,
        explanation: '15% of 200 = 0.15 × 200 = 30',
        points: 10
      },
      {
        id: '4-2',
        type: 'multiple-choice',
        question: 'What is the area of a circle with radius 5?',
        options: ['25π', '10π', '15π', '20π'],
        correctAnswer: 0,
        explanation: 'Area of circle = πr² = π × 5² = 25π',
        points: 10
      }
    ]
  },
  {
    id: '5',
    title: 'Science Trivia',
    description: 'General science questions covering physics, chemistry, and biology.',
    category: 'Science',
    difficulty: 'medium',
    createdBy: 'Dr. James Smith',
    createdAt: '2024-01-25',
    tags: ['science', 'physics', 'chemistry', 'biology'],
    isPublic: true,
    stats: {
      totalAttempts: 1876,
      averageScore: 69,
      completionRate: 87
    },
    questions: [
      {
        id: '5-1',
        type: 'multiple-choice',
        question: 'What is the chemical symbol for gold?',
        options: ['Go', 'Gd', 'Au', 'Ag'],
        correctAnswer: 2,
        explanation: 'Au comes from the Latin word "aurum" meaning gold.',
        points: 10
      },
      {
        id: '5-2',
        type: 'true-false',
        question: 'Light travels faster than sound.',
        correctAnswer: 'true',
        explanation: 'Light travels at approximately 300,000 km/s while sound travels at about 343 m/s.',
        points: 10
      }
    ]
  }
];

export const categories = [
  'Programming',
  'Geography',
  'Mathematics',
  'Science',
  'History',
  'Literature',
  'Sports',
  'Entertainment'
];

export const sampleUsers = [
  {
    id: '1',
    name: 'Alex Chen',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
    role: 'student' as const,
    stats: {
      totalQuizzes: 47,
      averageScore: 87,
      streak: 12,
      rank: 156
    }
  },
  {
    id: '2',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
    role: 'teacher' as const,
    stats: {
      totalQuizzes: 23,
      averageScore: 92,
      streak: 8,
      rank: 45
    }
  }
];
