// Application Constants
export const APP_CONFIG = {
  name: 'QuizCraft AI',
  version: '1.0.0',
  description: 'Advanced AI-Powered Quiz Platform',
  author: 'QuizCraft Team',
  website: 'https://quizcraft.ai',
  contact: '<EMAIL>',
  maxFileSize: 50 * 1024 * 1024, // 50MB
  supportedFileTypes: ['.pdf', '.txt', '.doc', '.docx', '.jpg', '.jpeg', '.png'],
  defaultTimeout: 30000,
  maxRetries: 3
} as const;

// Quiz Configuration
export const QUIZ_CONSTANTS = {
  MIN_QUESTIONS: 1,
  MAX_QUESTIONS: 100,
  DEFAULT_QUESTIONS: 10,
  MIN_TIME_LIMIT: 5, // minutes
  MAX_TIME_LIMIT: 180, // minutes
  DEFAULT_TIME_LIMIT: 30,
  MIN_QUESTION_TIME: 10, // seconds
  MAX_QUESTION_TIME: 300, // seconds
  DEFAULT_QUESTION_TIME: 30,
  PASS_THRESHOLD: 70, // percentage
  MAX_QUIZ_TITLE_LENGTH: 100,
  MAX_QUIZ_DESCRIPTION_LENGTH: 500,
  MAX_QUESTION_LENGTH: 1000,
  MAX_OPTION_LENGTH: 200,
  MAX_EXPLANATION_LENGTH: 1000
} as const;

// Difficulty Settings
export const DIFFICULTY_SETTINGS = {
  beginner: {
    timeMultiplier: 1.5,
    pointsMultiplier: 0.8,
    hintsAllowed: 3,
    color: 'green',
    bgColor: 'bg-green-100',
    textColor: 'text-green-700'
  },
  intermediate: {
    timeMultiplier: 1.0,
    pointsMultiplier: 1.0,
    hintsAllowed: 2,
    color: 'yellow',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-700'
  },
  advanced: {
    timeMultiplier: 0.7,
    pointsMultiplier: 1.3,
    hintsAllowed: 1,
    color: 'red',
    bgColor: 'bg-red-100',
    textColor: 'text-red-700'
  },
  adaptive: {
    timeMultiplier: 1.0,
    pointsMultiplier: 1.0,
    hintsAllowed: 2,
    color: 'purple',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-700'
  }
} as const;

// Rate Limiting Configuration
export const RATE_LIMIT_CONFIG = {
  // API endpoint limits (requests per window)
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100, // per window per IP
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false
  },

  // Quiz-specific rate limits
  quiz: {
    creation: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 10, // per user per hour
      message: 'Too many quiz creation attempts. Please try again in an hour.'
    },
    attempts: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 50, // per user per hour
      message: 'Too many quiz attempts. Please try again in an hour.'
    },
    generation: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 20, // per user per hour (AI generation is expensive)
      message: 'Too many AI generation requests. Please try again in an hour.'
    }
  },

  // User-specific limits
  user: {
    login: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      maxRequests: 5, // per IP
      message: 'Too many login attempts. Please try again in 15 minutes.'
    },
    registration: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3, // per IP
      message: 'Too many registration attempts. Please try again in an hour.'
    },
    passwordReset: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 3, // per email
      message: 'Too many password reset requests. Please try again in an hour.'
    }
  },

  // Anonymous user limits (stricter)
  anonymous: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20, // per IP
    message: 'Rate limit exceeded. Please register for higher limits.'
  },

  // File upload limits
  upload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10, // per user per hour
    maxSize: 10 * 1024 * 1024, // 10MB per file
    message: 'Too many file uploads. Please try again in an hour.'
  },

  // Multiplayer specific limits
  multiplayer: {
    roomCreation: {
      windowMs: 60 * 60 * 1000, // 1 hour
      maxRequests: 5, // per user per hour
      message: 'Too many room creation attempts. Please try again in an hour.'
    },
    gameActions: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100, // per user per minute
      message: 'Too many game actions. Please slow down.'
    }
  }
} as const;

// Security Configuration
export const SECURITY_CONFIG = {
  // CSRF Protection
  csrf: {
    enabled: true,
    cookieName: 'quizcraft_csrf',
    headerName: 'X-CSRF-Token',
    sameSite: 'strict' as const
  },

  // Content Security Policy
  csp: {
    enabled: true,
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "data:"],
      connectSrc: ["'self'", "https:"],
      mediaSrc: ["'self'", "https:"]
    }
  },

  // Input Validation
  validation: {
    maxInputLength: 10000,
    allowedTags: ['b', 'i', 'u', 'strong', 'em', 'br', 'p'],
    sanitizeHtml: true,
    validateUrls: true
  },

  // Session Security
  session: {
    secure: true,
    httpOnly: true,
    sameSite: 'strict' as const,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    rolling: true
  }
} as const;

// Multiplayer Configuration
export const MULTIPLAYER_CONFIG = {
  MIN_PLAYERS: 2,
  MAX_PLAYERS: 16,
  DEFAULT_MAX_PLAYERS: 4,
  ROOM_CODE_LENGTH: 6,
  MAX_ROOM_NAME_LENGTH: 50,
  GAME_COUNTDOWN: 5, // seconds
  QUESTION_COUNTDOWN: 3, // seconds
  RESULTS_DISPLAY_TIME: 10, // seconds
  HEARTBEAT_INTERVAL: 30000, // 30 seconds
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 3000 // 3 seconds
} as const;

// Game Modes Configuration
export const GAME_MODES = {
  speed: {
    name: 'Speed Challenge',
    description: 'Race against time',
    timeBonus: true,
    eliminationThreshold: 0,
    scoringWeight: { accuracy: 0.6, speed: 0.4 }
  },
  accuracy: {
    name: 'Accuracy Battle',
    description: 'Precision over speed',
    timeBonus: false,
    eliminationThreshold: 0,
    scoringWeight: { accuracy: 1.0, speed: 0.0 }
  },
  survival: {
    name: 'Last One Standing',
    description: 'Elimination style',
    timeBonus: true,
    eliminationThreshold: 50, // percentage
    scoringWeight: { accuracy: 0.8, speed: 0.2 }
  }
} as const;

// Animation Durations (in milliseconds)
export const ANIMATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000,
  PAGE_TRANSITION: 400,
  MODAL_TRANSITION: 250,
  TOAST_DURATION: 4000,
  TOOLTIP_DELAY: 500
} as const;

// Color Palette
export const COLORS = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a'
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d'
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f'
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d'
  }
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password'
  },
  QUIZZES: {
    LIST: '/quizzes',
    CREATE: '/quizzes',
    GET: '/quizzes/:id',
    UPDATE: '/quizzes/:id',
    DELETE: '/quizzes/:id',
    GENERATE: '/quizzes/generate',
    FEATURED: '/quizzes/featured',
    POPULAR: '/quizzes/popular',
    SEARCH: '/quizzes/search'
  },
  ATTEMPTS: {
    START: '/attempts/start',
    SUBMIT: '/attempts/submit',
    COMPLETE: '/attempts/complete',
    LIST: '/attempts',
    GET: '/attempts/:id',
    ANALYTICS: '/attempts/analytics'
  },
  MULTIPLAYER: {
    ROOMS: '/multiplayer/rooms',
    CREATE_ROOM: '/multiplayer/rooms',
    JOIN_ROOM: '/multiplayer/rooms/:id/join',
    LEAVE_ROOM: '/multiplayer/rooms/:id/leave',
    START_GAME: '/multiplayer/rooms/:id/start'
  },
  ANALYTICS: {
    USER: '/analytics/user',
    QUIZ: '/analytics/quiz/:id',
    LEADERBOARD: '/analytics/leaderboard',
    INSIGHTS: '/analytics/insights'
  },
  CONTENT: {
    UPLOAD: '/content/upload',
    EXTRACT_URL: '/content/extract-url',
    PROCESS: '/content/process',
    OCR: '/content/ocr'
  }
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied. Please check your permissions.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'An internal server error occurred. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size exceeds the maximum limit.',
  UNSUPPORTED_FILE_TYPE: 'This file type is not supported.',
  QUIZ_NOT_FOUND: 'Quiz not found or has been deleted.',
  ROOM_FULL: 'This game room is full.',
  ROOM_NOT_FOUND: 'Game room not found or has expired.',
  ALREADY_IN_ROOM: 'You are already in a game room.',
  GAME_IN_PROGRESS: 'Cannot join - game is already in progress.',
  INSUFFICIENT_PERMISSIONS: 'You do not have permission to perform this action.'
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  QUIZ_CREATED: 'Quiz created successfully!',
  QUIZ_UPDATED: 'Quiz updated successfully!',
  QUIZ_DELETED: 'Quiz deleted successfully!',
  QUIZ_COMPLETED: 'Congratulations! Quiz completed successfully!',
  ROOM_CREATED: 'Game room created successfully!',
  ROOM_JOINED: 'Successfully joined the game room!',
  GAME_STARTED: 'Game has started! Good luck!',
  ANSWER_SUBMITTED: 'Answer submitted successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!'
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  USER: 'quizcraft_user',
  AUTH_TOKEN: 'quizcraft_auth_token',
  THEME: 'quizcraft_theme',
  LANGUAGE: 'quizcraft_language',
  PREFERENCES: 'quizcraft_preferences',
  RECENT_QUIZZES: 'quizcraft_recent_quizzes',
  DRAFT_QUIZ: 'quizcraft_draft_quiz',
  GAME_STATE: 'quizcraft_game_state'
} as const;

// Question Types Configuration
export const QUESTION_TYPES = {
  'multiple-choice': {
    name: 'Multiple Choice',
    description: 'Choose the best answer from 4 options',
    icon: 'CheckCircle',
    minOptions: 2,
    maxOptions: 6,
    defaultOptions: 4,
    allowMultiple: false
  },
  'true-false': {
    name: 'True/False',
    description: 'Simple yes or no questions',
    icon: 'ToggleLeft',
    minOptions: 2,
    maxOptions: 2,
    defaultOptions: 2,
    allowMultiple: false
  },
  'short-answer': {
    name: 'Short Answer',
    description: 'Brief text responses',
    icon: 'Edit3',
    minOptions: 0,
    maxOptions: 0,
    defaultOptions: 0,
    allowMultiple: false
  },
  'essay': {
    name: 'Essay',
    description: 'Long-form written responses',
    icon: 'FileText',
    minOptions: 0,
    maxOptions: 0,
    defaultOptions: 0,
    allowMultiple: false
  },
  'fill-blank': {
    name: 'Fill in the Blank',
    description: 'Complete the missing words',
    icon: 'Minus',
    minOptions: 0,
    maxOptions: 0,
    defaultOptions: 0,
    allowMultiple: true
  },
  'matching': {
    name: 'Matching',
    description: 'Match items from two lists',
    icon: 'Link',
    minOptions: 4,
    maxOptions: 10,
    defaultOptions: 6,
    allowMultiple: false
  }
} as const;

// Achievement Categories
export const ACHIEVEMENT_CATEGORIES = {
  learning: {
    name: 'Learning',
    description: 'Knowledge and skill development',
    color: 'blue',
    icon: 'BookOpen'
  },
  social: {
    name: 'Social',
    description: 'Community engagement and interaction',
    color: 'green',
    icon: 'Users'
  },
  performance: {
    name: 'Performance',
    description: 'High scores and accuracy',
    color: 'yellow',
    icon: 'Trophy'
  },
  consistency: {
    name: 'Consistency',
    description: 'Regular practice and habits',
    color: 'purple',
    icon: 'Calendar'
  },
  exploration: {
    name: 'Exploration',
    description: 'Trying new features and content',
    color: 'pink',
    icon: 'Compass'
  }
} as const;

// Default User Preferences
export const DEFAULT_PREFERENCES = {
  language: 'en',
  theme: 'light',
  notifications: true,
  sounds: true,
  autoPlay: false,
  difficulty: 'auto',
  timezone: 'UTC',
  dateFormat: 'MM/DD/YYYY',
  timeFormat: '12h'
} as const;

// Export utility function to get nested constant values
export function getConstant(path: string): any {
  const parts = path.split('.');
  let current: any = {
    APP_CONFIG,
    QUIZ_CONSTANTS,
    DIFFICULTY_SETTINGS,
    MULTIPLAYER_CONFIG,
    GAME_MODES,
    ANIMATIONS,
    COLORS,
    BREAKPOINTS,
    API_ENDPOINTS,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    STORAGE_KEYS,
    QUESTION_TYPES,
    ACHIEVEMENT_CATEGORIES,
    DEFAULT_PREFERENCES
  };

  for (const part of parts) {
    current = current[part];
    if (current === undefined) break;
  }

  return current;
}