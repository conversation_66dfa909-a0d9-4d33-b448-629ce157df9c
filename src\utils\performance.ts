/**
 * Performance Optimization Utilities
 * Provides caching, lazy loading, code splitting, and bundle optimization
 */

import { logger } from '../services/logger';
import { monitoringService } from '../services/monitoring';

// Cache implementation
class Cache<T> {
  private cache = new Map<string, { data: T; timestamp: number; ttl: number }>();
  private maxSize: number;

  constructor(maxSize = 100) {
    this.maxSize = maxSize;
  }

  set(key: string, data: T, ttl = 5 * 60 * 1000): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// Debounce function
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate = false
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;

  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };

    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

// Throttle function
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// Memoization decorator
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  keyGenerator?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>();

  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args);
    
    if (cache.has(key)) {
      return cache.get(key)!;
    }

    const result = func(...args);
    cache.set(key, result);
    
    return result;
  }) as T;
}

// Lazy loading utility
export class LazyLoader {
  private static observers = new Map<Element, IntersectionObserver>();

  static observe(
    element: Element,
    callback: () => void,
    options: IntersectionObserverInit = {}
  ): void {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          callback();
          observer.unobserve(element);
          this.observers.delete(element);
        }
      });
    }, {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    });

    observer.observe(element);
    this.observers.set(element, observer);
  }

  static unobserve(element: Element): void {
    const observer = this.observers.get(element);
    if (observer) {
      observer.unobserve(element);
      this.observers.delete(element);
    }
  }

  static disconnect(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers.clear();
  }
}

// Image lazy loading
export function lazyLoadImage(img: HTMLImageElement): void {
  if ('loading' in HTMLImageElement.prototype) {
    // Native lazy loading
    img.loading = 'lazy';
  } else {
    // Fallback to Intersection Observer
    LazyLoader.observe(img, () => {
      if (img.dataset.src) {
        img.src = img.dataset.src;
        img.removeAttribute('data-src');
      }
    });
  }
}

// Virtual scrolling utility
export class VirtualScroller {
  private container: HTMLElement;
  private itemHeight: number;
  private visibleCount: number;
  private totalCount: number;
  private renderItem: (index: number) => HTMLElement;
  private scrollTop = 0;

  constructor(
    container: HTMLElement,
    itemHeight: number,
    totalCount: number,
    renderItem: (index: number) => HTMLElement
  ) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.totalCount = totalCount;
    this.renderItem = renderItem;
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2;

    this.setupScrolling();
    this.render();
  }

  private setupScrolling(): void {
    this.container.addEventListener('scroll', throttle(() => {
      this.scrollTop = this.container.scrollTop;
      this.render();
    }, 16)); // ~60fps
  }

  private render(): void {
    const startIndex = Math.floor(this.scrollTop / this.itemHeight);
    const endIndex = Math.min(startIndex + this.visibleCount, this.totalCount);

    // Clear container
    this.container.innerHTML = '';

    // Add spacer for items above viewport
    if (startIndex > 0) {
      const spacer = document.createElement('div');
      spacer.style.height = `${startIndex * this.itemHeight}px`;
      this.container.appendChild(spacer);
    }

    // Render visible items
    for (let i = startIndex; i < endIndex; i++) {
      const item = this.renderItem(i);
      this.container.appendChild(item);
    }

    // Add spacer for items below viewport
    const remainingItems = this.totalCount - endIndex;
    if (remainingItems > 0) {
      const spacer = document.createElement('div');
      spacer.style.height = `${remainingItems * this.itemHeight}px`;
      this.container.appendChild(spacer);
    }
  }

  updateTotalCount(count: number): void {
    this.totalCount = count;
    this.render();
  }
}

// Bundle optimization utilities
export class BundleOptimizer {
  // Preload critical resources
  static preloadResource(href: string, as: string, crossorigin?: string): void {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (crossorigin) link.crossOrigin = crossorigin;
    document.head.appendChild(link);
  }

  // Prefetch non-critical resources
  static prefetchResource(href: string): void {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
  }

  // Dynamic import with error handling
  static async loadModule<T>(
    moduleFactory: () => Promise<T>,
    fallback?: () => T
  ): Promise<T> {
    try {
      const endTimer = monitoringService.startTimer('module_load');
      const module = await moduleFactory();
      endTimer();
      return module;
    } catch (error) {
      logger.error('Module loading failed', 'performance', { error });
      
      if (fallback) {
        logger.info('Using fallback module', 'performance');
        return fallback();
      }
      
      throw error;
    }
  }

  // Code splitting helper
  static createAsyncComponent<T>(
    loader: () => Promise<{ default: T }>,
    fallback?: React.ComponentType
  ) {
    return React.lazy(async () => {
      try {
        const endTimer = monitoringService.startTimer('component_load');
        const module = await loader();
        endTimer();
        return module;
      } catch (error) {
        logger.error('Component loading failed', 'performance', { error });
        
        if (fallback) {
          return { default: fallback };
        }
        
        throw error;
      }
    });
  }
}

// Performance monitoring hooks
export function usePerformanceMonitor(name: string) {
  const [startTime] = React.useState(() => performance.now());

  React.useEffect(() => {
    return () => {
      const duration = performance.now() - startTime;
      monitoringService.recordMetric(`component.${name}`, duration, { unit: 'ms' });
    };
  }, [name, startTime]);
}

// Resource optimization
export class ResourceOptimizer {
  private static imageCache = new Cache<string>(50);
  private static fontCache = new Cache<FontFace>(20);

  // Optimize image loading
  static async loadOptimizedImage(
    src: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'webp' | 'avif' | 'jpeg' | 'png';
    } = {}
  ): Promise<string> {
    const cacheKey = `${src}_${JSON.stringify(options)}`;
    
    // Check cache first
    const cached = this.imageCache.get(cacheKey);
    if (cached) return cached;

    try {
      // In a real app, this would call an image optimization service
      const optimizedSrc = this.buildOptimizedImageUrl(src, options);
      
      // Preload the image
      await this.preloadImage(optimizedSrc);
      
      this.imageCache.set(cacheKey, optimizedSrc);
      return optimizedSrc;
    } catch (error) {
      logger.error('Image optimization failed', 'performance', { src, error });
      return src; // Fallback to original
    }
  }

  private static buildOptimizedImageUrl(
    src: string,
    options: any
  ): string {
    const params = new URLSearchParams();
    
    if (options.width) params.append('w', options.width.toString());
    if (options.height) params.append('h', options.height.toString());
    if (options.quality) params.append('q', options.quality.toString());
    if (options.format) params.append('f', options.format);

    const separator = src.includes('?') ? '&' : '?';
    return `${src}${separator}${params.toString()}`;
  }

  private static preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = reject;
      img.src = src;
    });
  }

  // Font optimization
  static async loadOptimizedFont(
    family: string,
    url: string,
    descriptors?: FontFaceDescriptors
  ): Promise<FontFace> {
    const cacheKey = `${family}_${url}`;
    
    // Check cache first
    const cached = this.fontCache.get(cacheKey);
    if (cached) return cached;

    try {
      const fontFace = new FontFace(family, `url(${url})`, descriptors);
      await fontFace.load();
      
      document.fonts.add(fontFace);
      this.fontCache.set(cacheKey, fontFace);
      
      return fontFace;
    } catch (error) {
      logger.error('Font loading failed', 'performance', { family, url, error });
      throw error;
    }
  }
}

// Memory management
export class MemoryManager {
  private static cleanupTasks: Array<() => void> = [];

  static addCleanupTask(task: () => void): void {
    this.cleanupTasks.push(task);
  }

  static cleanup(): void {
    this.cleanupTasks.forEach(task => {
      try {
        task();
      } catch (error) {
        logger.error('Cleanup task failed', 'performance', { error });
      }
    });
    this.cleanupTasks = [];
  }

  static getMemoryUsage(): {
    used: number;
    total: number;
    percentage: number;
  } {
    const memory = (performance as any).memory;
    
    if (!memory) {
      return { used: 0, total: 0, percentage: 0 };
    }

    return {
      used: memory.usedJSHeapSize,
      total: memory.totalJSHeapSize,
      percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100
    };
  }

  static forceGarbageCollection(): void {
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
      logger.info('Garbage collection forced', 'performance');
    }
  }
}

// Export cache instance for global use
export const globalCache = new Cache(200);

// Setup automatic cleanup
window.addEventListener('beforeunload', () => {
  MemoryManager.cleanup();
  LazyLoader.disconnect();
});

// Export React import for TypeScript
import React from 'react';
