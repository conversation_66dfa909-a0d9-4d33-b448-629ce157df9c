/**
 * AI Service for Quiz Generation
 * Uses custom algorithms and free APIs for intelligent quiz creation
 */

interface QuizSettings {
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'adaptive';
  questionCount: number;
  timeLimit: number;
  questionTypes: ('multiple-choice' | 'true-false' | 'short-answer' | 'essay')[];
  category: string;
}

interface Question {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'short-answer' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string;
  explanation: string;
  points: number;
  timeLimit: number;
}

// Text processing utilities
class TextProcessor {
  static extractKeyTerms(text: string): string[] {
    // Remove common words and extract meaningful terms
    const commonWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
    ]);

    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.has(word))
      .filter((word, index, arr) => arr.indexOf(word) === index) // Remove duplicates
      .slice(0, 20); // Top 20 terms
  }

  static extractSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map(sentence => sentence.trim())
      .filter(sentence => sentence.length > 20)
      .slice(0, 50); // Limit for processing
  }

  static identifyDefinitions(text: string): Array<{term: string, definition: string}> {
    const definitionPatterns = [
      /(.+?)\s+is\s+(.+?)(?:[.!?]|$)/gi,
      /(.+?)\s+means\s+(.+?)(?:[.!?]|$)/gi,
      /(.+?)\s+refers to\s+(.+?)(?:[.!?]|$)/gi,
      /(.+?):\s*(.+?)(?:[.!?]|$)/gi
    ];

    const definitions: Array<{term: string, definition: string}> = [];
    
    definitionPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const term = match[1].trim();
        const definition = match[2].trim();
        
        if (term.length > 2 && term.length < 50 && definition.length > 10) {
          definitions.push({ term, definition });
        }
      }
    });

    return definitions.slice(0, 10);
  }
}

// Question generation algorithms
class QuestionGenerator {
  static generateMultipleChoice(content: string, keyTerms: string[]): Question[] {
    const questions: Question[] = [];
    const definitions = TextProcessor.identifyDefinitions(content);
    const sentences = TextProcessor.extractSentences(content);

    // Generate definition-based questions
    definitions.forEach((def, index) => {
      if (questions.length >= 5) return;

      const distractors = this.generateDistractors(def.definition, keyTerms);
      const options = this.shuffleArray([def.definition, ...distractors]).slice(0, 4);

      questions.push({
        id: `mc_${Date.now()}_${index}`,
        type: 'multiple-choice',
        question: `What is ${def.term}?`,
        options,
        correctAnswer: def.definition,
        explanation: `${def.term} is defined as: ${def.definition}`,
        points: 1,
        timeLimit: 30
      });
    });

    // Generate fill-in-the-blank style questions
    sentences.forEach((sentence, index) => {
      if (questions.length >= 10) return;

      const words = sentence.split(' ');
      const importantWord = words.find(word => 
        keyTerms.some(term => term.toLowerCase() === word.toLowerCase())
      );

      if (importantWord && words.length > 5) {
        const questionText = sentence.replace(importantWord, '_____');
        const distractors = this.generateWordDistractors(importantWord, keyTerms);
        const options = this.shuffleArray([importantWord, ...distractors]).slice(0, 4);

        questions.push({
          id: `mc_fill_${Date.now()}_${index}`,
          type: 'multiple-choice',
          question: `Fill in the blank: ${questionText}`,
          options,
          correctAnswer: importantWord,
          explanation: `The correct word is "${importantWord}" as it fits the context of the sentence.`,
          points: 1,
          timeLimit: 30
        });
      }
    });

    return questions;
  }

  static generateTrueFalse(content: string, keyTerms: string[]): Question[] {
    const questions: Question[] = [];
    const sentences = TextProcessor.extractSentences(content);
    const definitions = TextProcessor.identifyDefinitions(content);

    // Generate true statements from definitions
    definitions.forEach((def, index) => {
      if (questions.length >= 3) return;

      questions.push({
        id: `tf_true_${Date.now()}_${index}`,
        type: 'true-false',
        question: `${def.term} is ${def.definition}`,
        correctAnswer: 'true',
        explanation: `This statement is true. ${def.term} is indeed ${def.definition}`,
        points: 1,
        timeLimit: 20
      });
    });

    // Generate false statements by modifying true ones
    definitions.forEach((def, index) => {
      if (questions.length >= 6) return;

      const randomTerm = keyTerms[Math.floor(Math.random() * keyTerms.length)];
      if (randomTerm && randomTerm !== def.term) {
        questions.push({
          id: `tf_false_${Date.now()}_${index}`,
          type: 'true-false',
          question: `${randomTerm} is ${def.definition}`,
          correctAnswer: 'false',
          explanation: `This statement is false. ${def.definition} describes ${def.term}, not ${randomTerm}`,
          points: 1,
          timeLimit: 20
        });
      }
    });

    return questions;
  }

  static generateShortAnswer(content: string, keyTerms: string[]): Question[] {
    const questions: Question[] = [];
    const definitions = TextProcessor.identifyDefinitions(content);

    definitions.forEach((def, index) => {
      if (questions.length >= 3) return;

      questions.push({
        id: `sa_${Date.now()}_${index}`,
        type: 'short-answer',
        question: `Define: ${def.term}`,
        correctAnswer: def.definition,
        explanation: `A complete answer should include: ${def.definition}`,
        points: 2,
        timeLimit: 60
      });
    });

    // Generate "What is" questions from key terms
    keyTerms.slice(0, 2).forEach((term, index) => {
      const context = this.findContextForTerm(content, term);
      if (context) {
        questions.push({
          id: `sa_what_${Date.now()}_${index}`,
          type: 'short-answer',
          question: `What is the significance of "${term}" in this context?`,
          correctAnswer: context,
          explanation: `The answer should explain how ${term} relates to the main topic.`,
          points: 2,
          timeLimit: 60
        });
      }
    });

    return questions;
  }

  // Helper methods
  private static generateDistractors(correct: string, keyTerms: string[]): string[] {
    const distractors: string[] = [];
    
    // Generate plausible but incorrect options
    const words = correct.split(' ');
    const templates = [
      `A process that involves ${keyTerms[0] || 'elements'}`,
      `A method used for ${keyTerms[1] || 'operations'}`,
      `A system designed to ${keyTerms[2] || 'function'}`,
      `A technique that enables ${keyTerms[3] || 'processing'}`
    ];

    templates.forEach(template => {
      if (template !== correct && distractors.length < 3) {
        distractors.push(template);
      }
    });

    return distractors;
  }

  private static generateWordDistractors(correct: string, keyTerms: string[]): string[] {
    return keyTerms
      .filter(term => term !== correct)
      .slice(0, 3);
  }

  private static findContextForTerm(content: string, term: string): string {
    const sentences = TextProcessor.extractSentences(content);
    const relevantSentence = sentences.find(sentence => 
      sentence.toLowerCase().includes(term.toLowerCase())
    );
    
    return relevantSentence || `Related to the main concepts discussed in the content.`;
  }

  private static shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}

// Difficulty adjustment algorithm
class DifficultyEngine {
  static adjustQuestionDifficulty(questions: Question[], targetDifficulty: string): Question[] {
    const difficultyMultipliers = {
      'beginner': { timeLimit: 1.5, points: 0.8 },
      'intermediate': { timeLimit: 1.0, points: 1.0 },
      'advanced': { timeLimit: 0.7, points: 1.3 },
      'adaptive': { timeLimit: 1.0, points: 1.0 }
    };

    const multiplier = difficultyMultipliers[targetDifficulty as keyof typeof difficultyMultipliers] || 
                     difficultyMultipliers.intermediate;

    return questions.map(question => ({
      ...question,
      timeLimit: Math.round(question.timeLimit * multiplier.timeLimit),
      points: Math.round(question.points * multiplier.points)
    }));
  }

  static validateAndImproveQuestions(questions: Question[]): Question[] {
    return questions.map(question => {
      // Ensure minimum quality standards
      if (question.question.length < 10) {
        question.question = `Please explain: ${question.question}`;
      }

      if (question.explanation.length < 20) {
        question.explanation = `This answer is correct because it directly relates to the key concepts discussed in the source material.`;
      }

      // Ensure proper options for multiple choice
      if (question.type === 'multiple-choice' && (!question.options || question.options.length < 4)) {
        question.options = [
          question.correctAnswer,
          'Alternative option A',
          'Alternative option B',
          'Alternative option C'
        ];
      }

      return question;
    });
  }
}

// Main export function
export async function generateQuizFromText(
  content: string, 
  settings: QuizSettings
): Promise<Question[]> {
  try {
    if (!content || content.length < 100) {
      throw new Error('Content too short. Please provide at least 100 characters.');
    }

    // Extract key information from content
    const keyTerms = TextProcessor.extractKeyTerms(content);
    if (keyTerms.length === 0) {
      throw new Error('Unable to extract meaningful content. Please provide more detailed text.');
    }

    let allQuestions: Question[] = [];

    // Generate questions based on selected types
    if (settings.questionTypes.includes('multiple-choice')) {
      const mcQuestions = QuestionGenerator.generateMultipleChoice(content, keyTerms);
      allQuestions = [...allQuestions, ...mcQuestions];
    }

    if (settings.questionTypes.includes('true-false')) {
      const tfQuestions = QuestionGenerator.generateTrueFalse(content, keyTerms);
      allQuestions = [...allQuestions, ...tfQuestions];
    }

    if (settings.questionTypes.includes('short-answer')) {
      const saQuestions = QuestionGenerator.generateShortAnswer(content, keyTerms);
      allQuestions = [...allQuestions, ...saQuestions];
    }

    // If no questions generated, create some default ones
    if (allQuestions.length === 0) {
      allQuestions = QuestionGenerator.generateMultipleChoice(content, keyTerms);
    }

    // Adjust difficulty
    allQuestions = DifficultyEngine.adjustQuestionDifficulty(allQuestions, settings.difficulty);

    // Validate and improve questions
    allQuestions = DifficultyEngine.validateAndImproveQuestions(allQuestions);

    // Limit to requested count
    const finalQuestions = allQuestions.slice(0, settings.questionCount);

    if (finalQuestions.length === 0) {
      throw new Error('Unable to generate questions from the provided content.');
    }

    // Simulate API delay for realistic UX
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

    return finalQuestions;

  } catch (error) {
    console.error('Error generating quiz:', error);
    throw error;
  }
}

// Additional utility functions
export function analyzeContentReadability(content: string): {
  wordCount: number;
  sentenceCount: number;
  averageWordsPerSentence: number;
  complexity: 'simple' | 'moderate' | 'complex';
} {
  const words = content.split(/\s+/).filter(word => word.length > 0);
  const sentences = content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
  
  const wordCount = words.length;
  const sentenceCount = sentences.length;
  const averageWordsPerSentence = wordCount / sentenceCount;

  let complexity: 'simple' | 'moderate' | 'complex' = 'moderate';
  if (averageWordsPerSentence < 15) complexity = 'simple';
  else if (averageWordsPerSentence > 25) complexity = 'complex';

  return {
    wordCount,
    sentenceCount,
    averageWordsPerSentence: Math.round(averageWordsPerSentence * 10) / 10,
    complexity
  };
}

export function suggestQuizSettings(content: string): Partial<QuizSettings> {
  const analysis = analyzeContentReadability(content);
  
  return {
    difficulty: analysis.complexity === 'simple' ? 'beginner' : 
               analysis.complexity === 'complex' ? 'advanced' : 'intermediate',
    questionCount: Math.min(Math.max(Math.floor(analysis.wordCount / 100), 5), 20),
    timeLimit: analysis.complexity === 'complex' ? 45 : 30,
    questionTypes: analysis.wordCount > 500 ? 
      ['multiple-choice', 'true-false', 'short-answer'] : 
      ['multiple-choice', 'true-false']
  };
}