import { useEffect } from 'react';
import { useAuthStore } from '../store/authStore';

export const useAuth = () => {
  const { user, isAuthenticated, login, logout, updateUser, setLoading, setError, clearError } = useAuthStore();

  useEffect(() => {
    // Auto-refresh user data on mount
    if (isAuthenticated && user) {
      updateUser({ lastActive: new Date() });
    }
  }, [isAuthenticated, user, updateUser]);

  const handleLogin = async (credentials: { email: string; password: string }) => {
    setLoading(true);
    clearError();
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock successful login
      const mockUser = {
        id: '1',
        name: '<PERSON>',
        email: credentials.email,
        avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=100&h=100&fit=crop',
        role: 'student' as const,
        stats: {
          totalQuizzes: 47,
          averageScore: 87,
          streak: 12,
          rank: 156
        }
      };
      
      login(mockUser);
    } catch (error) {
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
  };

  return {
    user,
    isAuthenticated,
    login: handleLogin,
    logout: handleLogout,
    updateUser,
    isLoading: useAuthStore(state => state.isLoading),
    error: useAuthStore(state => state.error),
    clearError
  };
};