import React, { useEffect, useState } from 'react';
import { Clock, AlertTriangle } from 'lucide-react';

interface TimerProps {
  initialTime: number; // in seconds
  onTimeUp?: () => void;
  onTick?: (timeRemaining: number) => void;
  warningThreshold?: number; // seconds when to show warning
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export const Timer: React.FC<TimerProps> = ({
  initialTime,
  onTimeUp,
  onTick,
  warningThreshold = 30,
  size = 'md',
  showIcon = true,
  className = ''
}) => {
  const [timeRemaining, setTimeRemaining] = useState(initialTime);
  const [isRunning, setIsRunning] = useState(true);

  useEffect(() => {
    if (!isRunning || timeRemaining <= 0) return;

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        const newTime = prev - 1;
        
        if (onTick) {
          onTick(newTime);
        }
        
        if (newTime <= 0) {
          setIsRunning(false);
          if (onTimeUp) {
            onTimeUp();
          }
        }
        
        return newTime;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, timeRemaining, onTimeUp, onTick]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const isWarning = timeRemaining <= warningThreshold && timeRemaining > 0;
  const isExpired = timeRemaining <= 0;

  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const iconSizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const getColorClasses = () => {
    if (isExpired) return 'text-red-600 bg-red-50 border-red-200';
    if (isWarning) return 'text-orange-600 bg-orange-50 border-orange-200';
    return 'text-gray-700 bg-gray-50 border-gray-200';
  };

  return (
    <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${getColorClasses()} ${className}`}>
      {showIcon && (
        <div className="flex-shrink-0">
          {isWarning || isExpired ? (
            <AlertTriangle className={`${iconSizeClasses[size]} ${isExpired ? 'text-red-500' : 'text-orange-500'}`} />
          ) : (
            <Clock className={`${iconSizeClasses[size]} text-gray-500`} />
          )}
        </div>
      )}
      
      <span className={`font-mono font-medium ${sizeClasses[size]}`}>
        {formatTime(Math.max(0, timeRemaining))}
      </span>
      
      {isExpired && (
        <span className="text-xs font-medium text-red-600 ml-1">
          TIME UP
        </span>
      )}
    </div>
  );
};

// Countdown timer with visual progress
interface CountdownTimerProps {
  duration: number; // in seconds
  onComplete?: () => void;
  size?: number;
  strokeWidth?: number;
  showTime?: boolean;
  autoStart?: boolean;
}

export const CountdownTimer: React.FC<CountdownTimerProps> = ({
  duration,
  onComplete,
  size = 100,
  strokeWidth = 6,
  showTime = true,
  autoStart = true
}) => {
  const [timeRemaining, setTimeRemaining] = useState(duration);
  const [isRunning, setIsRunning] = useState(autoStart);

  useEffect(() => {
    if (!isRunning || timeRemaining <= 0) return;

    const interval = setInterval(() => {
      setTimeRemaining(prev => {
        const newTime = prev - 1;
        
        if (newTime <= 0) {
          setIsRunning(false);
          if (onComplete) {
            onComplete();
          }
        }
        
        return newTime;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, timeRemaining, onComplete]);

  const percentage = ((duration - timeRemaining) / duration) * 100;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getColor = () => {
    const remaining = timeRemaining / duration;
    if (remaining > 0.5) return '#10B981'; // green
    if (remaining > 0.25) return '#F59E0B'; // yellow
    return '#EF4444'; // red
  };

  return (
    <div className="relative inline-flex items-center justify-center">
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="#E5E7EB"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={getColor()}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-1000 ease-linear"
        />
      </svg>
      
      {showTime && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-mono font-semibold text-gray-900">
            {formatTime(Math.max(0, timeRemaining))}
          </span>
        </div>
      )}
    </div>
  );
};