/**
 * Comprehensive Analytics and Reporting Service
 * Provides detailed analytics, insights, and reporting capabilities
 */

import { apiClient } from './api';
import { logger } from './logger';
import { AnalyticsData, UserAnalytics, QuizAnalytics, PerformanceMetrics } from '../types';

interface AnalyticsFilters {
  startDate?: Date;
  endDate?: Date;
  userId?: string;
  quizId?: string;
  category?: string;
  difficulty?: string;
  groupBy?: 'day' | 'week' | 'month';
}

interface LearningInsights {
  strengths: Array<{ topic: string; score: number; confidence: number }>;
  weaknesses: Array<{ topic: string; score: number; recommendation: string }>;
  learningPath: Array<{ topic: string; priority: number; estimatedTime: number }>;
  skillProgression: Array<{ skill: string; currentLevel: number; targetLevel: number; progress: number }>;
  recommendations: Array<{ type: string; title: string; description: string; actionUrl?: string }>;
}

interface PerformanceReport {
  overview: {
    totalQuizzes: number;
    totalTime: number;
    averageScore: number;
    improvement: number;
    streak: number;
    rank: number;
  };
  trends: {
    scoreHistory: Array<{ date: string; score: number }>;
    timeHistory: Array<{ date: string; time: number }>;
    accuracyHistory: Array<{ date: string; accuracy: number }>;
  };
  categoryBreakdown: Array<{
    category: string;
    quizzes: number;
    averageScore: number;
    timeSpent: number;
    improvement: number;
  }>;
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    unlockedAt: Date;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  }>;
}

class AnalyticsService {
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 2 * 60 * 1000; // 2 minutes for analytics (shorter cache)

  /**
   * Get cached data if available and not expired
   */
  private getFromCache<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }

  /**
   * Store data in cache
   */
  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  /**
   * Get user analytics
   */
  async getUserAnalytics(userId: string, filters?: AnalyticsFilters): Promise<UserAnalytics> {
    const cacheKey = `user-analytics:${userId}:${JSON.stringify(filters)}`;
    const cached = this.getFromCache<UserAnalytics>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = this.buildFilterParams(filters);
      const response = await apiClient.request(`/analytics/user/${userId}?${params}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get user analytics');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get user analytics', 'analytics-service', { userId, error });
      throw error;
    }
  }

  /**
   * Get quiz analytics
   */
  async getQuizAnalytics(quizId: string, filters?: AnalyticsFilters): Promise<QuizAnalytics> {
    const cacheKey = `quiz-analytics:${quizId}:${JSON.stringify(filters)}`;
    const cached = this.getFromCache<QuizAnalytics>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = this.buildFilterParams(filters);
      const response = await apiClient.request(`/analytics/quiz/${quizId}?${params}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get quiz analytics');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get quiz analytics', 'analytics-service', { quizId, error });
      throw error;
    }
  }

  /**
   * Get performance metrics
   */
  async getPerformanceMetrics(userId: string, timeframe = '30d'): Promise<PerformanceMetrics> {
    const cacheKey = `performance:${userId}:${timeframe}`;
    const cached = this.getFromCache<PerformanceMetrics>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request(`/analytics/performance/${userId}?timeframe=${timeframe}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get performance metrics');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get performance metrics', 'analytics-service', { userId, error });
      throw error;
    }
  }

  /**
   * Get learning insights using AI analysis
   */
  async getLearningInsights(userId: string): Promise<LearningInsights> {
    const cacheKey = `insights:${userId}`;
    const cached = this.getFromCache<LearningInsights>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request(`/analytics/insights/${userId}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get learning insights');
      }

      // Cache insights for longer since they don't change as frequently
      this.cache.set(cacheKey, { data: response.data, timestamp: Date.now() });
      
      return response.data;
    } catch (error) {
      logger.error('Failed to get learning insights', 'analytics-service', { userId, error });
      throw error;
    }
  }

  /**
   * Generate comprehensive performance report
   */
  async generatePerformanceReport(userId: string, timeframe = '30d'): Promise<PerformanceReport> {
    try {
      logger.info('Generating performance report', 'analytics-service', { userId, timeframe });

      const response = await apiClient.request(`/analytics/report/${userId}?timeframe=${timeframe}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to generate performance report');
      }

      logger.trackUserAction('generate_report', { userId, timeframe });
      
      return response.data;
    } catch (error) {
      logger.error('Failed to generate performance report', 'analytics-service', { userId, error });
      throw error;
    }
  }

  /**
   * Get leaderboard data
   */
  async getLeaderboard(category?: string, timeframe = '7d', limit = 50): Promise<Array<{
    rank: number;
    userId: string;
    name: string;
    avatar: string;
    score: number;
    quizzes: number;
    streak: number;
  }>> {
    const cacheKey = `leaderboard:${category || 'all'}:${timeframe}:${limit}`;
    const cached = this.getFromCache<any>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = new URLSearchParams();
      if (category) params.append('category', category);
      params.append('timeframe', timeframe);
      params.append('limit', limit.toString());

      const response = await apiClient.request(`/analytics/leaderboard?${params}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get leaderboard');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get leaderboard', 'analytics-service', { category, error });
      throw error;
    }
  }

  /**
   * Get global statistics
   */
  async getGlobalStats(): Promise<{
    totalUsers: number;
    totalQuizzes: number;
    totalAttempts: number;
    averageScore: number;
    popularCategories: Array<{ name: string; count: number }>;
    topPerformers: Array<{ name: string; score: number }>;
    recentActivity: Array<{ type: string; description: string; timestamp: Date }>;
  }> {
    const cacheKey = 'global-stats';
    const cached = this.getFromCache<any>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const response = await apiClient.request('/analytics/global');

      if (!response.success) {
        throw new Error(response.error || 'Failed to get global stats');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get global stats', 'analytics-service', { error });
      throw error;
    }
  }

  /**
   * Track custom event
   */
  async trackEvent(eventName: string, properties: Record<string, any>, userId?: string): Promise<void> {
    try {
      await apiClient.request('/analytics/events', {
        method: 'POST',
        body: {
          eventName,
          properties,
          userId,
          timestamp: new Date().toISOString()
        }
      });

      logger.trackUserAction(eventName, properties);
    } catch (error) {
      logger.error('Failed to track event', 'analytics-service', { eventName, error });
      // Don't throw error for tracking failures
    }
  }

  /**
   * Get user progress over time
   */
  async getUserProgress(userId: string, skill?: string): Promise<Array<{
    date: string;
    score: number;
    accuracy: number;
    speed: number;
    confidence: number;
  }>> {
    const cacheKey = `progress:${userId}:${skill || 'all'}`;
    const cached = this.getFromCache<any>(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      const params = skill ? `?skill=${skill}` : '';
      const response = await apiClient.request(`/analytics/progress/${userId}${params}`);

      if (!response.success) {
        throw new Error(response.error || 'Failed to get user progress');
      }

      this.setCache(cacheKey, response.data);
      return response.data;
    } catch (error) {
      logger.error('Failed to get user progress', 'analytics-service', { userId, skill, error });
      throw error;
    }
  }

  /**
   * Export analytics data
   */
  async exportData(userId: string, format: 'csv' | 'json' | 'pdf', filters?: AnalyticsFilters): Promise<Blob> {
    try {
      logger.info('Exporting analytics data', 'analytics-service', { userId, format });

      const params = this.buildFilterParams(filters);
      const response = await fetch(`/api/analytics/export/${userId}?format=${format}&${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to export data');
      }

      logger.trackUserAction('export_analytics', { userId, format });
      
      return await response.blob();
    } catch (error) {
      logger.error('Failed to export analytics data', 'analytics-service', { userId, format, error });
      throw error;
    }
  }

  /**
   * Build filter parameters for API requests
   */
  private buildFilterParams(filters?: AnalyticsFilters): string {
    if (!filters) return '';

    const params = new URLSearchParams();
    
    if (filters.startDate) params.append('startDate', filters.startDate.toISOString());
    if (filters.endDate) params.append('endDate', filters.endDate.toISOString());
    if (filters.userId) params.append('userId', filters.userId);
    if (filters.quizId) params.append('quizId', filters.quizId);
    if (filters.category) params.append('category', filters.category);
    if (filters.difficulty) params.append('difficulty', filters.difficulty);
    if (filters.groupBy) params.append('groupBy', filters.groupBy);

    return params.toString();
  }

  /**
   * Clear analytics cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Create singleton instance
export const analyticsService = new AnalyticsService();

// Export types
export type { AnalyticsFilters, LearningInsights, PerformanceReport };
