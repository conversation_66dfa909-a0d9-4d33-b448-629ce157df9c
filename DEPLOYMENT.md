# QuizCraft AI - Production Deployment Guide

This guide provides comprehensive instructions for deploying QuizCraft AI to production environments.

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ and npm 8+
- **Docker** and Docker Compose
- **SSL Certificate** (for HTTPS)
- **Domain Name** configured with DNS

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/quizcraft-ai/quizcraft-frontend.git
cd quizcraft-frontend

# Copy environment configuration
cp .env.example .env

# Edit environment variables
nano .env
```

### 2. Configure Environment Variables

Update `.env` with your production values:

```env
# Production URLs
VITE_APP_URL=https://app.quizcraft.ai
VITE_API_BASE_URL=https://api.quizcraft.ai
VITE_WEBSOCKET_URL=wss://ws.quizcraft.ai

# Security
VITE_CSRF_ENABLED=true
VITE_CSP_ENABLED=true
VITE_RATE_LIMITING_ENABLED=true

# External Services
VITE_OPENAI_API_KEY=your_production_openai_key
VITE_SENTRY_DSN=your_production_sentry_dsn
VITE_GOOGLE_ANALYTICS_ID=your_production_ga_id
```

### 3. Build and Deploy

```bash
# Build for production
npm run build:production

# Deploy with Docker Compose
npm run docker:compose

# Or deploy manually
npm run docker:build
npm run docker:run
```

## 🐳 Docker Deployment

### Single Container Deployment

```bash
# Build production image
docker build -t quizcraft-ai .

# Run container
docker run -d \
  --name quizcraft-ai \
  -p 80:80 \
  -p 443:443 \
  -v ./ssl:/etc/ssl/certs:ro \
  quizcraft-ai
```

### Multi-Container Deployment

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f frontend

# Stop services
docker-compose down
```

## ☁️ Cloud Platform Deployment

### AWS Deployment

#### Using AWS ECS

1. **Create ECR Repository**
```bash
aws ecr create-repository --repository-name quizcraft-ai
```

2. **Build and Push Image**
```bash
# Get login token
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

# Build and tag image
docker build -t quizcraft-ai .
docker tag quizcraft-ai:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/quizcraft-ai:latest

# Push image
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/quizcraft-ai:latest
```

3. **Create ECS Task Definition**
```json
{
  "family": "quizcraft-ai",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::<account-id>:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "quizcraft-ai",
      "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/quizcraft-ai:latest",
      "portMappings": [
        {
          "containerPort": 80,
          "protocol": "tcp"
        }
      ],
      "essential": true,
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/quizcraft-ai",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

#### Using AWS Amplify

```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Initialize Amplify
amplify init

# Add hosting
amplify add hosting

# Deploy
amplify publish
```

### Google Cloud Platform

#### Using Cloud Run

```bash
# Build and submit to Cloud Build
gcloud builds submit --tag gcr.io/PROJECT-ID/quizcraft-ai

# Deploy to Cloud Run
gcloud run deploy quizcraft-ai \
  --image gcr.io/PROJECT-ID/quizcraft-ai \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Microsoft Azure

#### Using Container Instances

```bash
# Create resource group
az group create --name quizcraft-rg --location eastus

# Create container instance
az container create \
  --resource-group quizcraft-rg \
  --name quizcraft-ai \
  --image quizcraft-ai:latest \
  --dns-name-label quizcraft-ai \
  --ports 80 443
```

## 🔧 Configuration

### Nginx Configuration

The included `nginx.conf` provides:

- **SSL/TLS termination**
- **Gzip compression**
- **Security headers**
- **Rate limiting**
- **Static asset caching**
- **API proxying**
- **WebSocket support**

### SSL/HTTPS Setup

1. **Obtain SSL Certificate**
```bash
# Using Let's Encrypt
certbot certonly --webroot -w /usr/share/nginx/html -d quizcraft.ai -d www.quizcraft.ai
```

2. **Configure SSL in nginx.conf**
```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /etc/letsencrypt/live/quizcraft.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/quizcraft.ai/privkey.pem;
    # ... rest of configuration
}
```

### Environment-Specific Configurations

#### Staging Environment
```env
VITE_APP_ENVIRONMENT=staging
VITE_API_BASE_URL=https://staging-api.quizcraft.ai
VITE_SENTRY_ENVIRONMENT=staging
```

#### Production Environment
```env
VITE_APP_ENVIRONMENT=production
VITE_API_BASE_URL=https://api.quizcraft.ai
VITE_SENTRY_ENVIRONMENT=production
VITE_ENABLE_DEBUG_MODE=false
```

## 📊 Monitoring and Logging

### Health Checks

The application includes comprehensive health checks:

```bash
# Manual health check
curl -f http://localhost/health

# Automated health check script
./scripts/health-check.sh
```

### Monitoring Stack

The Docker Compose setup includes:

- **Prometheus** - Metrics collection
- **Grafana** - Metrics visualization
- **Elasticsearch** - Log aggregation
- **Kibana** - Log analysis

Access monitoring dashboards:
- Grafana: http://localhost:3000
- Kibana: http://localhost:5601
- Prometheus: http://localhost:9090

### Application Monitoring

Configure external monitoring services:

```env
# Error tracking
VITE_SENTRY_DSN=your_sentry_dsn

# Performance monitoring
VITE_NEW_RELIC_LICENSE_KEY=your_newrelic_key

# Analytics
VITE_GOOGLE_ANALYTICS_ID=your_ga_id
```

## 🔒 Security

### Security Headers

The application implements comprehensive security headers:

- **Content Security Policy (CSP)**
- **X-Frame-Options**
- **X-Content-Type-Options**
- **X-XSS-Protection**
- **Strict-Transport-Security**

### Rate Limiting

Built-in rate limiting protects against abuse:

- API endpoints: 100 requests per 15 minutes
- Authentication: 5 attempts per 15 minutes
- General requests: 30 requests per second

### Input Validation

All user inputs are validated and sanitized:

- Client-side validation with real-time feedback
- Server-side validation for all API endpoints
- XSS prevention through input sanitization

## 🚨 Troubleshooting

### Common Issues

#### Application Won't Start
```bash
# Check container logs
docker logs quizcraft-ai

# Check health endpoint
curl -f http://localhost/health

# Verify environment variables
docker exec quizcraft-ai env | grep VITE_
```

#### SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in /etc/ssl/certs/quizcraft.ai.crt -text -noout

# Test SSL configuration
openssl s_client -connect quizcraft.ai:443
```

#### Performance Issues
```bash
# Check resource usage
docker stats quizcraft-ai

# Analyze bundle size
npm run build:analyze

# Check network performance
curl -w "@curl-format.txt" -o /dev/null -s http://localhost/
```

### Log Analysis

```bash
# Application logs
docker logs -f quizcraft-ai

# Nginx access logs
docker exec quizcraft-ai tail -f /var/log/nginx/access.log

# Nginx error logs
docker exec quizcraft-ai tail -f /var/log/nginx/error.log
```

## 📈 Performance Optimization

### Build Optimization

```bash
# Analyze bundle size
npm run build:analyze

# Enable production optimizations
NODE_ENV=production npm run build
```

### Runtime Optimization

- **Gzip compression** enabled
- **Static asset caching** with long expiry
- **CDN integration** ready
- **Image optimization** implemented
- **Code splitting** for optimal loading

## 🔄 CI/CD Pipeline

### GitHub Actions Example

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm run test:ci
        
      - name: Build application
        run: npm run build:production
        
      - name: Build Docker image
        run: docker build -t quizcraft-ai .
        
      - name: Deploy to production
        run: |
          # Add your deployment commands here
          echo "Deploying to production..."
```

## 📞 Support

For deployment support:

- **Documentation**: [docs.quizcraft.ai](https://docs.quizcraft.ai)
- **Issues**: [GitHub Issues](https://github.com/quizcraft-ai/quizcraft-frontend/issues)
- **Email**: <EMAIL>
- **Discord**: [QuizCraft Community](https://discord.gg/quizcraft)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
