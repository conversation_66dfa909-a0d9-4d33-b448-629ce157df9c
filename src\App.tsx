import React, { useState, useEffect } from 'react';
import { Brain, Users, BarChart3, Settings as SettingsIcon, LogOut, Menu, X, User } from 'lucide-react';

// Store
import { useAuthStore } from './store/authStore';

// Components
import { Loading } from './components/ui/Loading';

// Import pages directly
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';
import QuizCreator from './pages/QuizCreator';
import QuizTaker from './pages/QuizTaker';
import Analytics from './pages/Analytics';
import Multiplayer from './pages/Multiplayer';
import Profile from './pages/Profile';
import SettingsPage from './pages/Settings';

type Page = 'home' | 'dashboard' | 'create' | 'take' | 'analytics' | 'multiplayer' | 'profile' | 'settings';

function App() {
  const [currentPage, setCurrentPage] = useState<Page>('home');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, isAuthenticated, logout } = useAuthStore();

  // Handle authentication state changes
  useEffect(() => {
    if (isAuthenticated && currentPage === 'home') {
      setCurrentPage('dashboard');
    } else if (!isAuthenticated && currentPage !== 'home') {
      setCurrentPage('home');
    }
  }, [isAuthenticated, currentPage]);

  const navigation = [
    { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
    { id: 'create', name: 'Create Quiz', icon: Brain },
    { id: 'take', name: 'Take Quiz', icon: SettingsIcon },
    { id: 'multiplayer', name: 'Multiplayer', icon: Users },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'settings', name: 'Settings', icon: SettingsIcon },
  ];

  const handleNavigation = (pageId: string) => {
    setCurrentPage(pageId as Page);
    setIsMobileMenuOpen(false);
  };

  const handleLogout = () => {
    logout();
    setCurrentPage('home');
    setIsMobileMenuOpen(false);
  };

  const renderPage = () => {
    const pageProps = {
      onNavigate: handleNavigation,
      currentUser: user,
    };

    switch (currentPage) {
      case 'home':
        return <Home />;
      case 'dashboard':
        return <Dashboard {...pageProps} />;
      case 'create':
        return <QuizCreator {...pageProps} />;
      case 'take':
        return <QuizTaker {...pageProps} />;
      case 'analytics':
        return <Analytics {...pageProps} />;
      case 'multiplayer':
        return <Multiplayer {...pageProps} />;
      case 'profile':
        return <Profile />;
      case 'settings':
        return <SettingsPage />;
      default:
        return isAuthenticated ? <Dashboard {...pageProps} /> : <Home />;
    }
  };

  // Show Home page when not authenticated
  if (!isAuthenticated) {
    return <Home />;
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center gap-2">
            <Brain className="w-8 h-8 text-indigo-600" />
            <span className="text-xl font-bold text-gray-900">QuizCraft AI</span>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="lg:hidden text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <nav className="mt-6 px-3">
          <div className="space-y-1">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              return (
                <button
                  key={item.id}
                  onClick={() => handleNavigation(item.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    isActive
                      ? 'bg-indigo-50 text-indigo-700 border-r-2 border-indigo-700'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  {item.name}
                </button>
              );
            })}
          </div>
        </nav>

        {/* User Profile Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="flex items-center gap-3 mb-3">
            <img
              src={user?.avatar}
              alt={user?.name}
              className="w-10 h-10 rounded-full"
            />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">{user?.name}</p>
              <p className="text-xs text-gray-500 truncate">{user?.email}</p>
            </div>
          </div>
          <button
            onClick={handleLogout}
            className="w-full flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="w-4 h-4" />
            Sign Out
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        <div className="lg:hidden flex items-center justify-between h-16 px-4 bg-white border-b border-gray-200">
          <button
            onClick={() => setIsMobileMenuOpen(true)}
            className="text-gray-500 hover:text-gray-700"
          >
            <Menu className="w-6 h-6" />
          </button>
          <div className="flex items-center gap-2">
            <Brain className="w-6 h-6 text-indigo-600" />
            <span className="font-bold text-gray-900">QuizCraft AI</span>
          </div>
          <div className="w-6" /> {/* Spacer */}
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          {renderPage()}
        </main>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
    </div>
  );
}

export default App;
