import React, { useState, useEffect, Suspense } from 'react';
import { Brain, Users, BarChart3, Settings, LogOut, Menu, X, User } from 'lucide-react';

// Services
import { logger } from './services/logger';
import { monitoringService } from './services/monitoring';
import { errorHandler } from './services/error-handler';

// Components
import { Loading } from './components/ui/Loading';
import ErrorBoundary from './components/ui/ErrorBoundary';
import { ToastProvider } from './components/ui/Toast';

// Import pages directly to fix loading issues
import Home from './pages/Home';
import Dashboard from './pages/Dashboard';
import QuizCreator from './pages/QuizCreator';
import QuizTaker from './pages/QuizTaker';
import Analytics from './pages/Analytics';
import Multiplayer from './pages/Multiplayer';
import Profile from './pages/Profile';
import SettingsPage from './pages/Settings';

import { useAuthStore } from './store/authStore';

type Page = 'home' | 'dashboard' | 'create' | 'take' | 'analytics' | 'multiplayer' | 'profile' | 'settings';

function App() {
  const [currentPage, setCurrentPage] = useState<Page>('home');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user, isAuthenticated, logout } = useAuthStore();

  useEffect(() => {
    // Initialize application services
    logger.info('Application starting', 'app');

    // Start monitoring in production
    if (process.env.NODE_ENV === 'production') {
      monitoringService.start();
    }

    // Setup global error handler
    errorHandler.onError((error) => {
      console.error('Global error:', error);
    });

    // Performance monitoring
    const endTimer = monitoringService.startTimer('app_initialization');

    // Handle authentication state changes
    if (isAuthenticated && currentPage === 'home') {
      setCurrentPage('dashboard');
    } else if (!isAuthenticated && currentPage !== 'home') {
      setCurrentPage('home');
    }

    return () => {
      endTimer();
      if (process.env.NODE_ENV === 'production') {
        monitoringService.stop();
      }
    };
  }, [isAuthenticated]);

  const navigation = [
    { id: 'dashboard', name: 'Dashboard', icon: BarChart3 },
    { id: 'create', name: 'Create Quiz', icon: Brain },
    { id: 'take', name: 'Take Quiz', icon: Settings },
    { id: 'multiplayer', name: 'Multiplayer', icon: Users },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'settings', name: 'Settings', icon: Settings },
  ];

  const renderPage = () => {
    const pageProps = {
      onNavigate: setCurrentPage,
      onBack: () => setCurrentPage('dashboard')
    };

    switch (currentPage) {
      case 'home':
        return <Home />;
      case 'dashboard':
        return <Dashboard {...pageProps} />;
      case 'create':
        return <QuizCreator {...pageProps} />;
      case 'take':
        return <QuizTaker {...pageProps} />;
      case 'analytics':
        return <Analytics {...pageProps} />;
      case 'multiplayer':
        return <Multiplayer {...pageProps} />;
      case 'profile':
        return <Profile />;
      case 'settings':
        return <SettingsPage />;
      default:
        return isAuthenticated ? <Dashboard {...pageProps} /> : <Home />;
    }
  };

  // Show Home page when not authenticated
  if (!isAuthenticated) {
    return (
      <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
        <Home />
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
      <ToastProvider>
        <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={`${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'} 
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        
        {/* Header */}
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              QuizCraft AI
            </span>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(false)}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* User Profile */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <img
              src={user?.avatar}
              alt={user?.name}
              className="w-10 h-10 rounded-full object-cover"
            />
            <div>
              <p className="text-sm font-medium text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-500 capitalize">{user?.role}</p>
            </div>
          </div>
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-lg font-bold text-indigo-600">{user?.stats.streak}</p>
              <p className="text-xs text-gray-500">Day Streak</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-green-600">{user?.stats.averageScore}%</p>
              <p className="text-xs text-gray-500">Avg Score</p>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              return (
                <li key={item.id}>
                  <button
                    onClick={() => {
                      setCurrentPage(item.id as Page);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      isActive
                        ? 'bg-indigo-100 text-indigo-700 border-r-2 border-indigo-700'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.name}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={logout}
            className="w-full flex items-center space-x-3 px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="w-5 h-5" />
            <span className="font-medium">Sign Out</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Mobile Header */}
        <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600"
            >
              <Menu className="w-6 h-6" />
            </button>
            <div className="flex items-center space-x-2">
              <Brain className="w-6 h-6 text-indigo-600" />
              <span className="text-lg font-semibold text-gray-900">QuizCraft AI</span>
            </div>
            <div className="w-8" />
          </div>
        </div>

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          {renderPage()}
        </main>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}
      </div>
    </ToastProvider>
  </ErrorBoundary>
);
}

export default App;