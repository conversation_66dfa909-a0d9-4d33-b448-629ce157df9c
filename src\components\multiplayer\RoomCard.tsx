import React from 'react';
import { Users, Clock, Zap, Target, Trophy, Crown, Lock } from 'lucide-react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';

interface Room {
  id: string;
  name: string;
  host: string;
  players: Array<{
    id: string;
    name: string;
    avatar: string;
    isHost: boolean;
  }>;
  maxPlayers: number;
  quiz: string;
  status: 'waiting' | 'active' | 'finished';
  gameMode: 'speed' | 'accuracy' | 'survival';
  difficulty: 'easy' | 'medium' | 'hard';
  isPrivate: boolean;
}

interface RoomCardProps {
  room: Room;
  onJoin: (roomId: string) => void;
  onSpectate?: (roomId: string) => void;
  currentUserId?: string;
  className?: string;
}

export const RoomCard: React.FC<RoomCardProps> = ({
  room,
  onJoin,
  onSpectate,
  currentUserId,
  className = ''
}) => {
  const isUserInRoom = room.players.some(player => player.id === currentUserId);
  const isFull = room.players.length >= room.maxPlayers;
  const canJoin = room.status === 'waiting' && !isFull && !isUserInRoom;
  const canSpectate = room.status === 'active' && onSpectate;

  const getGameModeIcon = (mode: string) => {
    switch (mode) {
      case 'speed':
        return <Zap className="w-4 h-4 text-yellow-600" />;
      case 'accuracy':
        return <Target className="w-4 h-4 text-blue-600" />;
      case 'survival':
        return <Trophy className="w-4 h-4 text-red-600" />;
      default:
        return <Zap className="w-4 h-4 text-gray-600" />;
    }
  };

  const getGameModeName = (mode: string) => {
    switch (mode) {
      case 'speed':
        return 'Speed Challenge';
      case 'accuracy':
        return 'Accuracy Battle';
      case 'survival':
        return 'Last One Standing';
      default:
        return 'Unknown Mode';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'bg-green-100 text-green-700';
      case 'medium':
        return 'bg-yellow-100 text-yellow-700';
      case 'hard':
        return 'bg-red-100 text-red-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'bg-blue-100 text-blue-700';
      case 'active':
        return 'bg-green-100 text-green-700';
      case 'finished':
        return 'bg-gray-100 text-gray-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'waiting':
        return 'Waiting for players';
      case 'active':
        return 'Game in progress';
      case 'finished':
        return 'Game finished';
      default:
        return 'Unknown status';
    }
  };

  return (
    <Card className={`hover:shadow-md transition-shadow ${className}`} hover>
      <CardContent className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {room.name}
              </h3>
              {room.isPrivate && (
                <Lock className="w-4 h-4 text-gray-500" />
              )}
            </div>
            <p className="text-sm text-gray-600 truncate">{room.quiz}</p>
          </div>
          
          <div className="flex flex-col items-end space-y-1">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(room.status)}`}>
              {getStatusText(room.status)}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(room.difficulty)}`}>
              {room.difficulty}
            </span>
          </div>
        </div>

        {/* Game Mode */}
        <div className="flex items-center space-x-2 mb-4">
          {getGameModeIcon(room.gameMode)}
          <span className="text-sm text-gray-600">{getGameModeName(room.gameMode)}</span>
        </div>

        {/* Players */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-1">
              <Users className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                {room.players.length}/{room.maxPlayers} players
              </span>
            </div>
            
            {room.status === 'waiting' && (
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">Waiting</span>
              </div>
            )}
          </div>

          {/* Player Avatars */}
          <div className="flex items-center space-x-2">
            {room.players.slice(0, 4).map((player) => (
              <div key={player.id} className="relative">
                <img
                  src={player.avatar}
                  alt={player.name}
                  className="w-8 h-8 rounded-full object-cover border-2 border-white shadow-sm"
                  title={player.name}
                />
                {player.isHost && (
                  <Crown className="absolute -top-1 -right-1 w-3 h-3 text-yellow-500" />
                )}
              </div>
            ))}
            
            {room.players.length > 4 && (
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-xs font-medium text-gray-600">
                  +{room.players.length - 4}
                </span>
              </div>
            )}
            
            {/* Empty slots */}
            {room.players.length < room.maxPlayers && room.status === 'waiting' && (
              <>
                {Array.from({ length: Math.min(4 - room.players.length, room.maxPlayers - room.players.length) }).map((_, index) => (
                  <div
                    key={`empty-${index}`}
                    className="w-8 h-8 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center"
                  >
                    <Users className="w-3 h-3 text-gray-400" />
                  </div>
                ))}
              </>
            )}
          </div>
        </div>

        {/* Host Info */}
        <div className="mb-4">
          <div className="flex items-center space-x-2">
            <Crown className="w-4 h-4 text-yellow-500" />
            <span className="text-sm text-gray-600">
              Hosted by <span className="font-medium">{room.host}</span>
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          {canJoin && (
            <Button
              onClick={() => onJoin(room.id)}
              className="flex-1"
              size="sm"
            >
              Join Room
            </Button>
          )}
          
          {canSpectate && (
            <Button
              onClick={() => onSpectate!(room.id)}
              variant="outline"
              className="flex-1"
              size="sm"
            >
              Spectate
            </Button>
          )}
          
          {room.status === 'finished' && (
            <Button
              variant="outline"
              className="flex-1"
              size="sm"
              disabled
            >
              Game Ended
            </Button>
          )}
          
          {isFull && room.status === 'waiting' && !isUserInRoom && (
            <Button
              variant="outline"
              className="flex-1"
              size="sm"
              disabled
            >
              Room Full
            </Button>
          )}
          
          {isUserInRoom && (
            <Button
              variant="secondary"
              className="flex-1"
              size="sm"
              disabled
            >
              Already Joined
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};